<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    {!! Theme::header() !!}

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts - Roboto -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- Fancybox CSS - Sử dụng phiên bản 4 thay vì 5 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fancyapps/ui@4.0/dist/fancybox.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ Theme::asset()->url('css/style.css?v=3.1.5') }}">

    <!-- Popup Modal Styles -->
    <style>
        /* Success Modal */
        .success-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .success-modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .success-modal-content {
            position: relative;
            background: white;
            border-radius: 20px;
            padding: 40px 30px;
            max-width: 450px;
            width: 90%;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        .success-modal-header {
            margin-bottom: 20px;
        }

        .success-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #28a745, #20c997);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            animation: iconPulse 0.6s ease-out;
        }

        .success-icon i {
            font-size: 40px;
            color: white;
        }

        .success-title {
            color: #333;
            font-size: 24px;
            font-weight: 600;
            margin: 0;
        }

        .success-modal-body {
            margin-bottom: 30px;
        }

        .success-message {
            font-size: 16px;
            color: #666;
            margin-bottom: 10px;
        }

        .success-sub-message {
            font-size: 14px;
            color: #999;
            margin: 0;
        }

        .success-close-btn {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .success-close-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
        }

        /* Error Modal */
        .error-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .error-modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .error-modal-content {
            position: relative;
            background: white;
            border-radius: 20px;
            padding: 40px 30px;
            max-width: 450px;
            width: 90%;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        .error-modal-header {
            margin-bottom: 20px;
        }

        .error-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #dc3545, #c82333);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            animation: iconShake 0.6s ease-out;
        }

        .error-icon i {
            font-size: 40px;
            color: white;
        }

        .error-title {
            color: #333;
            font-size: 24px;
            font-weight: 600;
            margin: 0;
        }

        .error-modal-body {
            margin-bottom: 30px;
        }

        .error-message {
            font-size: 16px;
            color: #666;
            margin: 0;
        }

        .error-close-btn {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .error-close-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
        }

        /* Animations */
        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }

            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes iconPulse {
            0% {
                transform: scale(0.8);
            }

            50% {
                transform: scale(1.1);
            }

            100% {
                transform: scale(1);
            }
        }

        @keyframes iconShake {

            0%,
            100% {
                transform: translateX(0);
            }

            25% {
                transform: translateX(-5px);
            }

            75% {
                transform: translateX(5px);
            }
        }

        /* Responsive */
        @media (max-width: 768px) {

            .success-modal-content,
            .error-modal-content {
                padding: 30px 20px;
                margin: 20px;
            }

            .success-icon,
            .error-icon {
                width: 60px;
                height: 60px;
            }

            .success-icon i,
            .error-icon i {
                font-size: 30px;
            }

            .success-title,
            .error-title {
                font-size: 20px;
            }
        }
    </style>
</head>

<body>
    <!-- Header -->
    <header class="header fixed-top">
        <nav class="navbar navbar-expand-lg navbar-dark">
            <div class="container">
                <a class="navbar-brand" href="#home">
                    <img src="{{ Theme::asset()->url('images/logo.png') }}" alt="{{ theme_option('logo_alt_text', 'GREEN CITY BÌNH DƯƠNG') }}" height="50">
                </a>

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav ms-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="#tong-quan">{{ theme_option('nav_tong_quan', 'Tổng Quan') }}</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#vi-tri">{{ theme_option('nav_vi_tri', 'Vị Trí') }}</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#tien-ich">{{ theme_option('nav_tien_ich', 'Tiện Ích') }}</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#mat-bang">{{ theme_option('nav_mat_bang', 'Mặt Bằng') }}</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#thanh-toan">{{ theme_option('nav_thanh_toan', 'Thanh Toán') }}</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#hinh-anh">{{ theme_option('nav_hinh_anh', 'Hình Ảnh') }}</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#video">{{ theme_option('nav_video', 'Video') }}</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#lien-he">{{ theme_option('nav_lien_he', 'Liên Hệ') }}</a>
                        </li>
                    </ul>

                    <a href="tel:{{ theme_option('contact_phone', '0708808891') }}" class="btn btn-primary ms-3">
                        <i class="fas fa-phone"></i> {{ theme_option('contact_phone', '0708808891') }}
                    </a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section id="home" class="hero-section">
        <div class="hero-video-container">
            <iframe
                    src="{{ theme_option('hero_video_url', 'https://www.youtube.com/embed/Y_Mewkp3RvY?autoplay=1&mute=1&loop=1&playlist=Y_Mewkp3RvY&controls=0&showinfo=0&modestbranding=1&fs=0&cc_load_policy=0&iv_load_policy=3&autohide=0&rel=0') }}"
                    frameborder="0"
                    allow="autoplay; encrypted-media"
                    allowfullscreen
                    loading="lazy"
                    class="hero-video">
            </iframe>
        </div>
    </section>


    <!-- Tổng Quan Section -->
    <section id="tong-quan" class="section py-5 tong-quan-section">
        <div class="tong-quan-background"></div>
        <div class="container-fluid">
            <div class="row justify-content-end">
                <div class="col-lg-8">
                    <div class="section-content">
                        <h2 class="section-title text-white mb-5">{{ theme_option('tong_quan_title', 'TỔNG QUAN') }}</h2>
                        <div class="project-info-compact">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="info-group">
                                        <div class="info-item">
                                            <strong>{{ theme_option('tong_quan_label_ten_du_an', 'Tên dự án:') }}</strong>
                                            <span>{{ theme_option('project_name', 'Green City Bình Dương') }}</span>
                                        </div>
                                        <div class="info-item">
                                            <strong>{{ theme_option('tong_quan_label_chu_dau_tu', 'Chủ đầu tư:') }}</strong>
                                            <span>{{ theme_option('project_developer', 'Becamex IDC') }}</span>
                                        </div>
                                        <div class="info-item">
                                            <strong>{{ theme_option('tong_quan_label_vi_tri', 'Vị trí:') }}</strong>
                                            <span>{{ theme_option('project_location', 'Phường Bình Dương, Tp Hồ Chí Minh') }}</span>
                                        </div>
                                        <div class="info-item">
                                            <strong>{{ theme_option('tong_quan_label_dien_tich', 'Diện tích & Số lượng sản phẩm:') }}</strong>
                                            <span>{{ theme_option('project_area_units', '20 ha gần 1400 căn') }}</span>
                                        </div>
                                        <div class="info-item">
                                            <strong>{{ theme_option('tong_quan_label_loai_hinh', 'Loại hình sản phẩm:') }}</strong>
                                            <span>{{ theme_option('project_product_types', 'Shophouse, Nhà Phố Liền Kề') }}</span>
                                        </div>
                                        <div class="info-item">
                                            <strong>{{ theme_option('tong_quan_label_quy_chuan', 'Quy chuẩn xây dựng:') }}</strong>
                                            <span>{{ theme_option('project_building_standards', '1 Trệt – 1 Lửng – 2 Lầu (110m² – 150m²)') }}</span>
                                        </div>
                                        <div class="info-item">
                                            <strong>{{ theme_option('tong_quan_label_thoi_gian', 'Thời gian bàn giao:') }}</strong>
                                            <span>{{ theme_option('project_handover_time', 'Dự kiến năm 2026') }}</span>
                                        </div>
                                        <div class="info-item">
                                            <strong>{{ theme_option('tong_quan_label_ngan_hang', 'Ngân hàng hỗ trợ:') }}</strong>
                                            <span>{{ theme_option('project_bank_support', 'BIDV') }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Vị Trí Section -->
    <section id="vi-tri" class="section py-5 bg-light">
        <div class="container" style="max-width: 90%;">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title" data-aos="fade-up">{{ theme_option('vi_tri_title', 'VỊ TRÍ GREEN CITY BÌNH DƯƠNG') }}</h2>
                    <p class="section-subtitle" data-aos="fade-up" data-aos-delay="200">{{ theme_option('vi_tri_subtitle', 'THÀNH PHỐ MỚI BÌNH DƯƠNG') }}</p>
                </div>
            </div>

            <div class="row align-items-center mb-5">
                <div class="col-lg-5" data-aos="fade-right">
                    <div class="location-content" style="height: 600px">
                        <h4>{{ theme_option('vi_tri_content_title_1', 'Green City Bình Dương – Đòn bẩy tăng trưởng giữa trung tâm kinh tế trọng điểm') }}</h4>

                        <p>{{ theme_option('vi_tri_content_desc_1', 'Green City Bình Dương tọa lạc ngay trung tâm Thành phố mới Bình Dương ngay góc đường huyết mạch Đại lộ Lê Lợi, Võ Nguyên Giáp. Dự án nằm trong vùng quy hoạch chiến lược, kết nối trực tiếp đến Trung tâm Hành Chính, KCN Mỹ Phước, KCN Vsip II, Quốc lộ 13, mở ra cơ hội sinh lời vượt trội.') }}</p>

                        <h4>{{ theme_option('vi_tri_content_title_2', 'Green City Bình Dương – Nơi khởi đầu cuộc sống xanh giữa lòng đô thị mới') }}</h4>

                        <p>{{ theme_option('vi_tri_content_desc_2', 'Dự án sở hữu môi trường sống trong lành, quy hoạch hiện đại, tiện ích đồng bộ – là nơi lý tưởng để xây dựng tổ ấm, an cư lâu dài giữa vùng đô thị phát triển năng động bậc nhất miền Nam.') }}</p>
                    </div>
                </div>
                <div class="col-lg-7" data-aos="fade-left">
                    <div class="location-image">
                        @if (theme_option('vi_tri_image'))
                            <img src="{{ RvMedia::getImageUrl(theme_option('vi_tri_image')) }}" alt="Vị trí Green City Bình Dương" class="img-fluid rounded shadow">
                        @else
                            <img src="{{ Theme::asset()->url('images/green-city-binh-duong-3.jpg') }}" alt="Vị trí Green City Bình Dương" class="img-fluid rounded shadow">
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Liên kết vùng Section -->
    <section id="vi-tri" class="section py-5 lien-ket-vung-section">
        <div class="lien-ket-vung-background"></div>
        <div class="container" style="max-width: 90%;">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title text-white" data-aos="fade-up">{{ theme_option('lien_ket_vung_title', 'LIÊN KẾT VÙNG GREEN CITY BÌNH DƯƠNG') }}</h2>
                    <p class="section-subtitle text-white-50" data-aos="fade-up" data-aos-delay="200">{{ theme_option('lien_ket_vung_subtitle', 'Liên kết vùng & Tiềm năng phát triển của Green City Bình Dương') }}</p>
                </div>
            </div>

            <div class="row align-items-center">
                <div class="col-lg-5" data-aos="fade-right">
                    <div class="lien-ket-vung-content">
                        <h4>{{ theme_option('lien_ket_vung_content_title', 'Liên kết Vùng chiến lược của Green City Bình Dương') }}</h4>

                        <p>{{ theme_option('lien_ket_vung_content_desc', 'Green City Bình Dương nằm trong lòng Trung tâm Thành phố mới Bình Dương – khu vực đầu tàu quy hoạch theo mô hình đô thị thông minh, hiện đại và đa chức năng. Đây chính là trung tâm hành chính, tài chính, giáo dục và công nghệ cao của tỉnh Bình Dương.') }}</p>

                        <ul class="strategic-connections">
                            <li><strong>{{ theme_option('lien_ket_vung_item_1_title', 'Nằm trên trục Đại lộ Bình Dương') }}</strong> – {{ theme_option('lien_ket_vung_item_1_desc', 'tuyến đường xương sống kết nối từ trung tâm TP.HCM đến phía bắc tỉnh Bình Dương.') }}</li>

                            <li><strong>{{ theme_option('lien_ket_vung_item_2_title', 'Giao cắt với Võ Nguyên Giáp và Đại lộ Lê Lợi') }}</strong> – {{ theme_option('lien_ket_vung_item_2_desc', 'tạo mạng lưới giao thông đa chiều trong đô thị. Kết nối Đông Tây với việc kết nối DT741 và DT742.') }}</li>

                            <li><strong>{{ theme_option('lien_ket_vung_item_3_title', 'Liền kề Vành đai 4') }}</strong>, {{ theme_option('lien_ket_vung_item_3_desc', 'giúp kết nối TP.HCM – Đồng Nai – Tây Ninh, tạo thành trục phát triển liên vùng nhanh chóng và hiệu quả.') }}</li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-7" data-aos="fade-left">
                    <div class="lien-ket-vung-map">
                        @if (theme_option('lien_ket_vung_map_image'))
                            <img src="{{ RvMedia::getImageUrl(theme_option('lien_ket_vung_map_image')) }}" alt="Bản đồ liên kết vùng Green City Bình Dương" class="img-fluid rounded shadow">
                        @else
                            <img src="{{ Theme::asset()->url('images/map.png') }}" alt="Bản đồ liên kết vùng Green City Bình Dương" class="img-fluid rounded shadow">
                        @endif
                        <div class="map-caption">
                            <h5>{{ theme_option('vi_tri_lien_ket_title', 'Vị trí liên kết vùng') }}</h5>
                            <p>{{ theme_option('vi_tri_lien_ket_desc', 'Sơ đồ mạng lưới giao thông và kết nối chiến lược của Green City Bình Dương') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Tiện Ích Section -->
    <section id="tien-ich" class="section py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title" data-aos="fade-up">{{ theme_option('tien_ich_title', 'TIỆN ÍCH GREEN CITY BÌNH DƯƠNG') }}</h2>
                </div>
            </div>

            <div class="row mb-5">
                <div class="col-12">
                    <h3 class="amenities-title" data-aos="fade-up">{{ theme_option('tien_ich_noi_khu_title', 'Tiện ích nội khu:') }}</h3>
                    <div class="row">
                        <div class="col-md-6 col-lg-4 mb-4" data-aos="fade-up" data-aos-delay="100">
                            <div class="amenity-card">
                                <div class="amenity-image">
                                    @php
                                        $image_url = theme_option('tien_ich_noi_khu_1_image') ? RvMedia::getImageUrl(theme_option('tien_ich_noi_khu_1_image')) : Theme::asset()->url('images/tien-ich-noi-khu/green-school-truong-mam-non.jpg');
                                        $title = theme_option('tien_ich_noi_khu_1_title', 'Green School');
                                        $desc = theme_option('tien_ich_noi_khu_1_desc', 'Trường mầm non với thương hiệu Green School');
                                    @endphp
                                    <a href="{{ $image_url }}" data-fancybox="tien-ich-noi-khu" data-caption="{{ $title }} - {{ $desc }}">
                                        <img src="{{ $image_url }}" alt="{{ $title }} - {{ $desc }}" class="img-fluid">
                                        <div class="amenity-overlay">
                                            <i class="fas fa-school"></i>
                                        </div>
                                    </a>
                                </div>
                                <div class="amenity-content">
                                    <h5>{{ $title }}</h5>
                                    <p>{{ $desc }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 col-lg-4 mb-4" data-aos="fade-up" data-aos-delay="200">
                            <div class="amenity-card">
                                <div class="amenity-image">
                                    @php
                                        $image_url = theme_option('tien_ich_noi_khu_2_image') ? RvMedia::getImageUrl(theme_option('tien_ich_noi_khu_2_image')) : Theme::asset()->url('images/tien-ich-noi-khu/green-mall-trung-tam-thuong-mai.jpg');
                                        $title = theme_option('tien_ich_noi_khu_2_title', 'Green Mall');
                                        $desc = theme_option('tien_ich_noi_khu_2_desc', 'Trung tâm thương mại đẳng cấp nội khu');
                                    @endphp
                                    <a href="{{ $image_url }}" data-fancybox="tien-ich-noi-khu" data-caption="{{ $title }} - {{ $desc }}">
                                        <img src="{{ $image_url }}" alt="{{ $title }} - {{ $desc }}" class="img-fluid">
                                        <div class="amenity-overlay">
                                            <i class="fas fa-shopping-center"></i>
                                        </div>
                                    </a>
                                </div>
                                <div class="amenity-content">
                                    <h5>{{ $title }}</h5>
                                    <p>{{ $desc }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 col-lg-4 mb-4" data-aos="fade-up" data-aos-delay="300">
                            <div class="amenity-card">
                                <div class="amenity-image">
                                    @php
                                        $image_url = theme_option('tien_ich_noi_khu_3_image') ? RvMedia::getImageUrl(theme_option('tien_ich_noi_khu_3_image')) : Theme::asset()->url('images/tien-ich-noi-khu/green-garden-cong-vien-vuon-di-bo.jpg');
                                        $title = theme_option('tien_ich_noi_khu_3_title', 'Green Garden');
                                        $desc = theme_option('tien_ich_noi_khu_3_desc', 'Công viên và vườn đi bộ cao cấp');
                                    @endphp
                                    <a href="{{ $image_url }}" data-fancybox="tien-ich-noi-khu" data-caption="{{ $title }} - {{ $desc }}">
                                        <img src="{{ $image_url }}" alt="{{ $title }} - {{ $desc }}" class="img-fluid">
                                        <div class="amenity-overlay">
                                            <i class="fas fa-tree"></i>
                                        </div>
                                    </a>
                                </div>
                                <div class="amenity-content">
                                    <h5>{{ $title }}</h5>
                                    <p>{{ $desc }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 col-lg-4 mb-4" data-aos="fade-up" data-aos-delay="400">
                            <div class="amenity-card">
                                <div class="amenity-image">
                                    @php
                                        $image_url = theme_option('tien_ich_noi_khu_4_image') ? RvMedia::getImageUrl(theme_option('tien_ich_noi_khu_4_image')) : Theme::asset()->url('images/tien-ich-noi-khu/khu-vuc-sinh-hoat-cong-dong.jpg');
                                        $title = theme_option('tien_ich_noi_khu_4_title', 'Không gian sinh hoạt chung');
                                        $desc = theme_option('tien_ich_noi_khu_4_desc', 'Khu vực sinh hoạt cộng đồng hiện đại');
                                    @endphp
                                    <a href="{{ $image_url }}" data-fancybox="tien-ich-noi-khu" data-caption="{{ $title }} - {{ $desc }}">
                                        <img src="{{ $image_url }}" alt="{{ $title }} - {{ $desc }}" class="img-fluid">
                                        <div class="amenity-overlay">
                                            <i class="fas fa-users"></i>
                                        </div>
                                    </a>
                                </div>
                                <div class="amenity-content">
                                    <h5>{{ $title }}</h5>
                                    <p>{{ $desc }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 col-lg-4 mb-4" data-aos="fade-up" data-aos-delay="500">
                            <div class="amenity-card">
                                <div class="amenity-image">
                                    @php
                                        $image_url = theme_option('tien_ich_noi_khu_5_image') ? RvMedia::getImageUrl(theme_option('tien_ich_noi_khu_5_image')) : Theme::asset()->url('images/tien-ich-noi-khu/green-gym-phong-tap-cao-cap.jpg');
                                        $title = theme_option('tien_ich_noi_khu_5_title', 'Green Gym');
                                        $desc = theme_option('tien_ich_noi_khu_5_desc', 'Phòng tập Gym cao cấp thương hiệu Green Gym');
                                    @endphp
                                    <a href="{{ $image_url }}" data-fancybox="tien-ich-noi-khu" data-caption="{{ $title }} - {{ $desc }}">
                                        <img src="{{ $image_url }}" alt="{{ $title }} - {{ $desc }}" class="img-fluid">
                                        <div class="amenity-overlay">
                                            <i class="fas fa-dumbbell"></i>
                                        </div>
                                    </a>
                                </div>
                                <div class="amenity-content">
                                    <h5>{{ $title }}</h5>
                                    <p>{{ $desc }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <h3 class="amenities-title" data-aos="fade-up">{{ theme_option('tien_ich_ngoai_khu_title', 'Tiện ích ngoại khu vượt trội:') }}</h3>
                    <p data-aos="fade-up" data-aos-delay="200">{{ theme_option('tien_ich_ngoai_khu_desc', 'Nằm ngay vị trí lõi thành phố mới Bình Dương, Green City thừa hưởng trọn vẹn hệ thống tiện ích hiện đại của khu vực:') }}</p>

                    <div class="row">
                        <div class="col-md-6 col-lg-4 mb-4" data-aos="fade-up" data-aos-delay="300">
                            <div class="amenity-card">
                                <div class="amenity-image">
                                    @php
                                        $image_url = theme_option('tien_ich_ngoai_khu_1_image') ? RvMedia::getImageUrl(theme_option('tien_ich_ngoai_khu_1_image')) : Theme::asset()->url('images/tien-ich-ngoai-khu/tthc-binh-duong-hanh-chinh.jpg');
                                        $title = theme_option('tien_ich_ngoai_khu_1_title', 'TTHC Bình Dương');
                                        $desc = theme_option('tien_ich_ngoai_khu_1_desc', 'Trung tâm Hành chính tỉnh Bình Dương');
                                    @endphp
                                    <a href="{{ $image_url }}" data-fancybox="tien-ich-ngoai-khu" data-caption="{{ $title }} - {{ $desc }}">
                                        <img src="{{ $image_url }}" alt="{{ $title }} - {{ $desc }}" class="img-fluid">
                                        <div class="amenity-overlay">
                                            <i class="fas fa-building"></i>
                                        </div>
                                    </a>
                                </div>
                                <div class="amenity-content">
                                    <h5>{{ $title }}</h5>
                                    <p>{{ $desc }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 col-lg-4 mb-4" data-aos="fade-up" data-aos-delay="400">
                            <div class="amenity-card">
                                <div class="amenity-image">
                                    @php
                                        $image_url = theme_option('tien_ich_ngoai_khu_2_image') ? RvMedia::getImageUrl(theme_option('tien_ich_ngoai_khu_2_image')) : Theme::asset()->url('images/tien-ich-ngoai-khu/kcn-vsip-2-khu-cong-nghiep.jpg');
                                        $title = theme_option('tien_ich_ngoai_khu_2_title', 'KCN VSIP II');
                                        $desc = theme_option('tien_ich_ngoai_khu_2_desc', 'Khu Công nghiệp VSIP II và các tập đoàn đa quốc gia');
                                    @endphp
                                    <a href="{{ $image_url }}" data-fancybox="tien-ich-ngoai-khu" data-caption="{{ $title }} - {{ $desc }}">
                                        <img src="{{ $image_url }}" alt="{{ $title }} - {{ $desc }}" class="img-fluid">
                                        <div class="amenity-overlay">
                                            <i class="fas fa-industry"></i>
                                        </div>
                                    </a>
                                </div>
                                <div class="amenity-content">
                                    <h5>{{ $title }}</h5>
                                    <p>{{ $desc }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 col-lg-4 mb-4" data-aos="fade-up" data-aos-delay="500">
                            <div class="amenity-card">
                                <div class="amenity-image">
                                    @php
                                        $image_url = theme_option('tien_ich_ngoai_khu_3_image') ? RvMedia::getImageUrl(theme_option('tien_ich_ngoai_khu_3_image')) : Theme::asset()->url('images/tien-ich-ngoai-khu/dai-hoc-quoc-te-mien-dong.jpg');
                                        $title = theme_option('tien_ich_ngoai_khu_3_title', 'Đại học Quốc tế Miền Đông');
                                        $desc = theme_option('tien_ich_ngoai_khu_3_desc', 'Mạng lưới trường học liên cấp chuẩn quốc tế');
                                    @endphp
                                    <a href="{{ $image_url }}" data-fancybox="tien-ich-ngoai-khu" data-caption="{{ $title }} - {{ $desc }}">
                                        <img src="{{ $image_url }}" alt="{{ $title }} - {{ $desc }}" class="img-fluid">
                                        <div class="amenity-overlay">
                                            <i class="fas fa-graduation-cap"></i>
                                        </div>
                                    </a>
                                </div>
                                <div class="amenity-content">
                                    <h5>{{ $title }}</h5>
                                    <p>{{ $desc }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 col-lg-4 mb-4" data-aos="fade-up" data-aos-delay="600">
                            <div class="amenity-card">
                                <div class="amenity-image">
                                    @php
                                        $image_url = theme_option('tien_ich_ngoai_khu_4_image') ? RvMedia::getImageUrl(theme_option('tien_ich_ngoai_khu_4_image')) : Theme::asset()->url('images/tien-ich-ngoai-khu/benh-vien-1500-giuong-y-te.jpg');
                                        $title = theme_option('tien_ich_ngoai_khu_4_title', 'Bệnh viện 1500 giường');
                                        $desc = theme_option('tien_ich_ngoai_khu_4_desc', 'Hệ thống y tế cao cấp và hiện đại');
                                    @endphp
                                    <a href="{{ $image_url }}" data-fancybox="tien-ich-ngoai-khu" data-caption="{{ $title }} - {{ $desc }}">
                                        <img src="{{ $image_url }}" alt="{{ $title }} - {{ $desc }}" class="img-fluid">
                                        <div class="amenity-overlay">
                                            <i class="fas fa-hospital"></i>
                                        </div>
                                    </a>
                                </div>
                                <div class="amenity-content">
                                    <h5>{{ $title }}</h5>
                                    <p>{{ $desc }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 col-lg-4 mb-4" data-aos="fade-up" data-aos-delay="700">
                            <div class="amenity-card">
                                <div class="amenity-image">
                                    @php
                                        $image_url = theme_option('tien_ich_ngoai_khu_5_image') ? RvMedia::getImageUrl(theme_option('tien_ich_ngoai_khu_5_image')) : Theme::asset()->url('images/tien-ich-ngoai-khu/aeon-mall-trung-tam-thuong-mai.jpg');
                                        $title = theme_option('tien_ich_ngoai_khu_5_title', 'AEON Mall');
                                        $desc = theme_option('tien_ich_ngoai_khu_5_desc', 'Trung tâm thương mại và siêu thị hiện đại');
                                    @endphp
                                    <a href="{{ $image_url }}" data-fancybox="tien-ich-ngoai-khu" data-caption="{{ $title }} - {{ $desc }}">
                                        <img src="{{ $image_url }}" alt="{{ $title }} - {{ $desc }}" class="img-fluid">
                                        <div class="amenity-overlay">
                                            <i class="fas fa-shopping-bag"></i>
                                        </div>
                                    </a>
                                </div>
                                <div class="amenity-content">
                                    <h5>{{ $title }}</h5>
                                    <p>{{ $desc }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 col-lg-4 mb-4" data-aos="fade-up" data-aos-delay="800">
                            <div class="amenity-card">
                                <div class="amenity-image">
                                    @php
                                        $image_url = theme_option('tien_ich_ngoai_khu_6_image') ? RvMedia::getImageUrl(theme_option('tien_ich_ngoai_khu_6_image')) : Theme::asset()->url('images/tien-ich-ngoai-khu/quoc-lo-13-giao-thong-huyet-mach.jpg');
                                        $title = theme_option('tien_ich_ngoai_khu_6_title', 'Quốc lộ 13');
                                        $desc = theme_option('tien_ich_ngoai_khu_6_desc', 'Kết nối các trục giao thông huyết mạch');
                                    @endphp
                                    <a href="{{ $image_url }}" data-fancybox="tien-ich-ngoai-khu" data-caption="{{ $title }} - {{ $desc }}">
                                        <img src="{{ $image_url }}" alt="{{ $title }} - {{ $desc }}" class="img-fluid">
                                        <div class="amenity-overlay">
                                            <i class="fas fa-road"></i>
                                        </div>
                                    </a>
                                </div>
                                <div class="amenity-content">
                                    <h5>{{ $title }}</h5>
                                    <p>{{ $desc }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Chủ Đầu Tư Section -->
    <section id="chu-dau-tu" class="section py-5 chu-dau-tu-section">
        <div class="chu-dau-tu-background"></div>
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title text-white" data-aos="fade-up">{{ theme_option('chu_dau_tu_title', 'CHỦ ĐẦU TƯ UY TÍN') }}</h2>
                    <p class="section-subtitle text-white-50" data-aos="fade-up" data-aos-delay="200">{{ theme_option('chu_dau_tu_subtitle', 'BECAMEX IDC - TẬP ĐOÀN HÀNG ĐẦU VIỆT NAM') }}</p>
                </div>
            </div>

            <div class="row justify-content-center">
                <div class="col-lg-10" data-aos="fade-up">
                    <div class="developer-content-compact">
                        <!-- Logo và giới thiệu chính -->
                        <div class="developer-main-info">
                            <div class="row align-items-center">
                                <div class="col-md-3 text-center mb-4 mb-md-0">
                                    <div class="developer-logo">
                                        @if (theme_option('chu_dau_tu_logo'))
                                            <img src="{{ RvMedia::getImageUrl(theme_option('chu_dau_tu_logo')) }}" alt="Becamex IDC Logo" class="img-fluid">
                                        @else
                                            <img src="{{ Theme::asset()->url('images/Becamex_Logo-01.png') }}" alt="Becamex IDC Logo" class="img-fluid">
                                        @endif
                                    </div>
                                </div>
                                <div class="col-md-9">
                                    <div class="developer-intro">
                                        <h4>{{ theme_option('chu_dau_tu_name', 'Tổng Công ty Đầu tư và Phát triển Công nghiệp (Becamex IDC)') }}</h4>
                                        <p>{{ theme_option('chu_dau_tu_description', 'Đơn vị phát triển hạ tầng và đô thị hàng đầu Việt Nam với hơn 30 năm kinh nghiệm. Becamex IDC đã thành công phát triển nhiều khu đô thị, khu công nghiệp quy mô lớn tại Bình Dương và các tỉnh thành khác.') }}</p>

                                        <div class="developer-highlights">
                                            <div class="highlight-item">
                                                <i class="fas fa-building"></i>
                                                <span>{{ theme_option('developer_highlight_1', 'Hơn 30 năm kinh nghiệm phát triển đô thị') }}</span>
                                            </div>
                                            <div class="highlight-item">
                                                <i class="fas fa-award"></i>
                                                <span>{{ theme_option('developer_highlight_2', 'Tập đoàn bất động sản uy tín hàng đầu') }}</span>
                                            </div>
                                            <div class="highlight-item">
                                                <i class="fas fa-handshake"></i>
                                                <span>{{ theme_option('developer_highlight_3', 'Đối tác tin cậy của nhiều tập đoàn quốc tế') }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Mặt Bằng Section -->
    <section id="mat-bang" class="section py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title" data-aos="fade-up">{{ theme_option('mat_bang_title', 'MẶT BẰNG DỰ ÁN') }}</h2>
                </div>
            </div>

            <div class="row">
                <div class="col-12" data-aos="fade-up">
                    <div class="masterplan-content">
                        <div class="masterplan-image text-center mt-4">
                            <img src="{{ theme_option('mat_bang_image', 'https://greencitybecamex.vn/wp-content/uploads/2025/03/green-city-binh-duong-mat-bang.jpg') }}" alt="Mặt bằng tổng thể Green City Bình Dương" class="img-fluid rounded shadow">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Thanh Toán Section -->
    <section id="thanh-toan" class="section py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title" data-aos="fade-up">{{ theme_option('thanh_toan_title', 'PHƯƠNG THỨC THANH TOÁN') }}</h2>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-12 mx-auto" data-aos="fade-up">
                    <div class="payment-info">
                        <h4>{{ theme_option('payment_loan_info_title', 'THÔNG TIN GÓI VAY') }}</h4>
                        <div class="loan-info">
                            <div class="loan-item">
                                <strong>{{ theme_option('payment_loan_interest_label', 'Khách hàng được ưu đãi lãi suất:') }}</strong>
                                <ul>
                                    <li>Lãi suất: {{ theme_option('loan_interest_rate', 'dự kiến 6,5%/năm') }}</li>
                                    <li>{{ theme_option('payment_loan_duration_label', 'Thời gian áp dụng: đến khi BIDV có văn bản thông báo dừng gói tín dụng') }}</li>
                                </ul>
                            </div>
                            <div class="loan-item">
                                <strong>{{ theme_option('payment_support_ratio_label', 'Tỷ lệ tài trợ:') }}</strong> {{ theme_option('loan_support_ratio', 'tối đa 70% giá trị Hợp đồng mua bán Nhà ở/đất ở ký kết với Chủ đầu tư') }}.
                            </div>
                            <div class="loan-item">
                                <strong>{{ theme_option('payment_loan_duration_value_label', 'Thời gian vay:') }}</strong> {{ theme_option('loan_duration', 'tối đa 30 năm') }}.
                            </div>
                        </div>

                        <div class="price-table mt-5">
                            <h4>{{ theme_option('price_table_title', 'BẢNG GIÁ BÁN GREEN CITY BÌNH DƯƠNG') }}</h4>

                            <!-- Desktop Table -->
                            <div class="table-responsive d-none d-lg-block">
                                <table class="table table-bordered">
                                    <thead class="table-success">
                                        <tr>
                                            <th>{{ theme_option('bang_gia_header_loai_sp', 'LOẠI SẢN PHẨM') }}</th>
                                            <th>{{ theme_option('bang_gia_header_dien_tich', 'DIỆN TÍCH M²') }}</th>
                                            <th>{{ theme_option('bang_gia_header_gia_thap', 'GIÁ THẤP NHẤT (TỶ ĐỒNG)') }}</th>
                                            <th>{{ theme_option('bang_gia_header_gia_cao', 'GIÁ CAO NHẤT (TỶ ĐỒNG)') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>{{ theme_option('bang_gia_lien_ke_title', 'Nhà liền kề') }}</td>
                                            <td>{{ theme_option('project_area_units', '110-150') }}</td>
                                            <td>{{ theme_option('bang_gia_cap_nhat_text', 'Cập nhật') }}</td>
                                            <td>{{ theme_option('bang_gia_cap_nhat_text', 'Cập nhật') }}</td>
                                        </tr>
                                        <tr>
                                            <td>{{ theme_option('bang_gia_shophouse_title', 'Shophouse') }}</td>
                                            <td>{{ theme_option('project_area_units', '110-150') }}</td>
                                            <td>{{ theme_option('bang_gia_cap_nhat_text', 'Cập nhật') }}</td>
                                            <td>{{ theme_option('bang_gia_cap_nhat_text', 'Cập nhật') }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Mobile Block Layout -->
                            <div class="price-blocks d-block d-lg-none">
                                <div class="price-block-item">
                                    <div class="price-block-header">
                                        <h5>{{ theme_option('bang_gia_lien_ke_title', 'Nhà liền kề') }}</h5>
                                    </div>
                                    <div class="price-block-body">
                                        <div class="price-detail">
                                            <span class="label">{{ theme_option('bang_gia_label_dien_tich', 'Diện tích:') }}</span>
                                            <span class="value">{{ theme_option('project_area_units', '110-150') }} m²</span>
                                        </div>
                                        <div class="price-detail">
                                            <span class="label">{{ theme_option('bang_gia_label_gia_thap', 'Giá thấp nhất:') }}</span>
                                            <span class="value">{{ theme_option('bang_gia_cap_nhat_text', 'Cập nhật') }}</span>
                                        </div>
                                        <div class="price-detail">
                                            <span class="label">{{ theme_option('bang_gia_label_gia_cao', 'Giá cao nhất:') }}</span>
                                            <span class="value">{{ theme_option('bang_gia_cap_nhat_text', 'Cập nhật') }}</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="price-block-item">
                                    <div class="price-block-header">
                                        <h5>{{ theme_option('bang_gia_shophouse_title', 'Shophouse') }}</h5>
                                    </div>
                                    <div class="price-block-body">
                                        <div class="price-detail">
                                            <span class="label">{{ theme_option('bang_gia_label_dien_tich', 'Diện tích:') }}</span>
                                            <span class="value">{{ theme_option('project_area_units', '110-150') }} m²</span>
                                        </div>
                                        <div class="price-detail">
                                            <span class="label">{{ theme_option('bang_gia_label_gia_thap', 'Giá thấp nhất:') }}</span>
                                            <span class="value">{{ theme_option('bang_gia_cap_nhat_text', 'Cập nhật') }}</span>
                                        </div>
                                        <div class="price-detail">
                                            <span class="label">{{ theme_option('bang_gia_label_gia_cao', 'Giá cao nhất:') }}</span>
                                            <span class="value">{{ theme_option('bang_gia_cap_nhat_text', 'Cập nhật') }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <p class="text-muted mt-3">
                                <strong>{{ theme_option('bang_gia_luu_y_label', 'Lưu ý:') }}</strong> {{ theme_option('price_note', 'Giá bán liền kề, shophouse Green City trung bình khoảng —, bảng giá Green City Bình Dương sẽ được chủ đầu tư Becamex IDC công bố đồng loạt từng lô trong ngày mở bán.') }}
                            </p>

                            <div class="text-center mt-4">
                                <a href="tel:{{ theme_option('contact_phone', '0708808891') }}" class="btn btn-primary btn-lg">
                                    <i class="fas fa-phone me-2"></i>
                                    {{ theme_option('contact_cta_button_text', 'TƯ VẤN & BÁO GIÁ: 0708808891') }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Hình Ảnh Section -->
    <section id="hinh-anh" class="section py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title" data-aos="fade-up">{{ theme_option('gallery_title', 'HÌNH ẢNH DỰ ÁN') }}</h2>
                </div>
            </div>

            <!-- Main Gallery Slider -->
            <div class="row mb-4">
                <div class="col-12" data-aos="fade-up">
                    <div id="mainGalleryCarousel" class="carousel slide gallery-carousel" data-bs-ride="carousel">
                        <!-- Indicators -->
                        <div class="carousel-indicators">
                            <button type="button" data-bs-target="#mainGalleryCarousel" data-bs-slide-to="0" class="active" aria-current="true" aria-label="Slide 1"></button>
                            <button type="button" data-bs-target="#mainGalleryCarousel" data-bs-slide-to="1" aria-label="Slide 2"></button>
                            <button type="button" data-bs-target="#mainGalleryCarousel" data-bs-slide-to="2" aria-label="Slide 3"></button>
                            <button type="button" data-bs-target="#mainGalleryCarousel" data-bs-slide-to="3" aria-label="Slide 4"></button>
                            <button type="button" data-bs-target="#mainGalleryCarousel" data-bs-slide-to="4" aria-label="Slide 5"></button>
                            <button type="button" data-bs-target="#mainGalleryCarousel" data-bs-slide-to="5" aria-label="Slide 6"></button>
                        </div>

                        <!-- Carousel Items -->
                        <div class="carousel-inner">
                            @php
                                $gallery_slides = theme_option('gallery_slides');
                                $slides = [];

                                if ($gallery_slides) {
                                    if (is_string($gallery_slides)) {
                                        $slides = json_decode($gallery_slides, true);
                                    } else {
                                        $slides = $gallery_slides;
                                    }
                                }

                                // Nếu slides từ theme option có dạng [key => value], chuyển đổi format
                                if (!empty($slides) && is_array($slides)) {
                                    $formattedSlides = [];
                                    foreach ($slides as $slide) {
                                        if (is_array($slide)) {
                                            // Nếu là array có key-value pairs, convert về format cần thiết
                                            if (isset($slide[0]) && is_array($slide[0])) {
                                                $slideData = [];
                                                foreach ($slide as $item) {
                                                    if (isset($item['key']) && isset($item['value'])) {
                                                        $slideData[$item['key']] = $item['value'];
                                                    }
                                                }
                                                $formattedSlides[] = $slideData;
                                            } else {
                                                $formattedSlides[] = $slide;
                                            }
                                        }
                                    }
                                    if (!empty($formattedSlides)) {
                                        $slides = $formattedSlides;
                                    }
                                }

                                // Default slides nếu không có data từ theme option
                                if (empty($slides)) {
                                    $slides = [
                                        [
                                            'image' => 'symlife-2-PN-3_11zon.jpg',
                                            'title' => 'Nhà mẫu The Felix - Phòng khách',
                                            'description' => 'Thiết kế nội thất hiện đại, sang trọng với không gian mở thoáng đãng',
                                        ],
                                        [
                                            'image' => 'symlife-2-PN-4_11zon.jpg',
                                            'title' => 'Nhà mẫu The Felix - Phòng ngủ master',
                                            'description' => 'Phòng ngủ chính với thiết kế ấm cúng, tiện nghi hiện đại',
                                        ],
                                        [
                                            'image' => 'symlife-2-PN-5_11zon.jpg',
                                            'title' => 'Nhà mẫu The Felix - Phòng bếp',
                                            'description' => 'Khu vực bếp và bàn ăn được thiết kế thông minh, tối ưu không gian',
                                        ],
                                        [
                                            'image' => 'symlife-2-PN-6_11zon.jpg',
                                            'title' => 'Nhà mẫu The Felix - Ban công',
                                            'description' => 'Ban công rộng rãi với view thoáng mát, không gian thư giãn lý tưởng',
                                        ],
                                        [
                                            'image' => 'symlife-2-PN-7_11zon.jpg',
                                            'title' => 'Nhà mẫu The Felix - Phòng tắm',
                                            'description' => 'Phòng tắm hiện đại với thiết bị vệ sinh cao cấp',
                                        ],
                                        [
                                            'image' => 'symlife-2-PN-8_11zon.jpg',
                                            'title' => 'Nhà mẫu The Felix - Không gian làm việc',
                                            'description' => 'Góc làm việc tại nhà được bố trí khéo léo, tạo sự tập trung',
                                        ],
                                    ];
                                }
                            @endphp

                            @foreach ($slides as $index => $slide)
                                <div class="carousel-item @if ($index == 0) active @endif">
                                    @if (isset($slide['image']) && $slide['image'])
                                        @if (str_starts_with($slide['image'], 'http'))
                                            <img loading="lazy" decoding="async" src="{{ $slide['image'] }}"
                                                 class="d-block w-100 gallery-main-img" alt="{{ $slide['title'] ?? 'Gallery Image' }}">
                                        @else
                                            <img loading="lazy" decoding="async" src="{{ RvMedia::getImageUrl($slide['image']) }}"
                                                 class="d-block w-100 gallery-main-img" alt="{{ $slide['title'] ?? 'Gallery Image' }}">
                                        @endif
                                    @else
                                        <img loading="lazy" decoding="async" src="{{ Theme::asset()->url('images/' . ($slide['image'] ?? 'default.jpg')) }}"
                                             class="d-block w-100 gallery-main-img" alt="{{ $slide['title'] ?? 'Gallery Image' }}">
                                    @endif
                                    <div class="carousel-caption d-none d-md-block">
                                        <h5>{{ $slide['title'] ?? '' }}</h5>
                                        <p>{{ $slide['description'] ?? '' }}</p>
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        <!-- Navigation Controls -->
                        <button class="carousel-control-prev" type="button" data-bs-target="#mainGalleryCarousel" data-bs-slide="prev">
                            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">{{ theme_option('carousel_previous_text', 'Previous') }}</span>
                        </button>
                        <button class="carousel-control-next" type="button" data-bs-target="#mainGalleryCarousel" data-bs-slide="next">
                            <span class="carousel-control-next-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">{{ theme_option('carousel_next_text', 'Next') }}</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Thumbnail Gallery -->
            <div class="row">
                <div class="col-12" data-aos="fade-up" data-aos-delay="200">
                    <div class="thumbnail-gallery">
                        <div class="row">
                            <div class="col-md-2 col-4 mb-3">
                                <div class="thumbnail-item active" data-bs-target="#mainGalleryCarousel" data-bs-slide-to="0">
                                    <img src="https://greencitybecamex.vn/wp-content/uploads/2023/11/symlife-2-PN-3_11zon-300x169.jpg"
                                         alt="Phòng khách" class="img-fluid rounded">
                                </div>
                            </div>
                            <div class="col-md-2 col-4 mb-3">
                                <div class="thumbnail-item" data-bs-target="#mainGalleryCarousel" data-bs-slide-to="1">
                                    <img src="https://greencitybecamex.vn/wp-content/uploads/2023/11/symlife-2-PN-4_11zon-300x169.jpg"
                                         alt="Phòng ngủ master" class="img-fluid rounded">
                                </div>
                            </div>
                            <div class="col-md-2 col-4 mb-3">
                                <div class="thumbnail-item" data-bs-target="#mainGalleryCarousel" data-bs-slide-to="2">
                                    <img src="https://greencitybecamex.vn/wp-content/uploads/2023/11/symlife-2-PN-5_11zon-300x169.jpg"
                                         alt="Phòng bếp" class="img-fluid rounded">
                                </div>
                            </div>
                            <div class="col-md-2 col-4 mb-3">
                                <div class="thumbnail-item" data-bs-target="#mainGalleryCarousel" data-bs-slide-to="3">
                                    <img src="https://greencitybecamex.vn/wp-content/uploads/2023/11/symlife-2-PN-6_11zon-300x169.jpg"
                                         alt="Ban công" class="img-fluid rounded">
                                </div>
                            </div>
                            <div class="col-md-2 col-4 mb-3">
                                <div class="thumbnail-item" data-bs-target="#mainGalleryCarousel" data-bs-slide-to="4">
                                    <img src="https://greencitybecamex.vn/wp-content/uploads/2023/11/symlife-2-PN-7_11zon-300x169.jpg"
                                         alt="Phòng tắm" class="img-fluid rounded">
                                </div>
                            </div>
                            <div class="col-md-2 col-4 mb-3">
                                <div class="thumbnail-item" data-bs-target="#mainGalleryCarousel" data-bs-slide-to="5">
                                    <img src="https://greencitybecamex.vn/wp-content/uploads/2023/11/symlife-2-PN-8_11zon-300x169.jpg"
                                         alt="Không gian làm việc" class="img-fluid rounded">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Video Section -->
    <section id="video" class="section py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title" data-aos="fade-up">{{ theme_option('video_title', 'VIDEO GIỚI THIỆU') }}</h2>
                </div>
            </div>
        </div>

        <div class="container-fluid px-0" data-aos="fade-up">
            <div class="video-container">
                <div class="ratio ratio-16x9">
                    <iframe src="{{ theme_option('video_embed_url', 'https://www.youtube.com/embed/9po0pfbZRNM?enablejsapi=1&origin=https://greencity-binhduong.com') }}" title="Video giới thiệu Green City" allowfullscreen></iframe>
                </div>
            </div>
        </div>
    </section>

    <!-- Liên Hệ Section -->
    <section id="lien-he" class="section py-5 bg-primary text-white">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title text-white" data-aos="fade-up">{{ theme_option('lien_he_title', 'LIÊN HỆ & ĐĂNG KÝ') }}</h2>
                    <p class="section-subtitle text-white-50" data-aos="fade-up" data-aos-delay="200">{{ theme_option('lien_he_subtitle', 'Đăng ký nhận thông tin và báo giá mới nhất') }}</p>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <div class="contact-form" data-aos="fade-up">
                        <form id="contactForm" action="{{ route('public.send.contact') }}" method="POST">
                            @csrf
                            <input type="hidden" name="agree_terms_and_policy" value="1">

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <input type="text" class="form-control" name="name" placeholder="{{ theme_option('form_name_placeholder', 'Họ và tên *') }}" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <input type="text" class="form-control" name="phone" placeholder="{{ theme_option('form_phone_placeholder', 'Số điện thoại *') }}" required>
                                </div>
                            </div>

                            <div class="mb-3">
                                <input type="email" class="form-control" name="email_display" placeholder="{{ theme_option('form_email_placeholder', 'Email (tùy chọn)') }}" value="<EMAIL>">
                            </div>

                            <div class="mb-3">
                                <label class="form-label text-white">{{ theme_option('form_product_select_label', 'Loại sản phẩm quan tâm:') }} <span class="text-warning">*</span></label>
                                <select class="form-control" name="content" required>
                                    <option value="">{{ theme_option('form_product_select_placeholder', 'Chọn loại sản phẩm') }}</option>
                                    <option value="{{ theme_option('product_option_1', 'Liền kề') }}" selected>{{ theme_option('product_option_1', 'Liền kề') }}</option>
                                    <option value="{{ theme_option('product_option_2', 'Shophouse') }}">{{ theme_option('product_option_2', 'Shophouse') }}</option>
                                    <option value="{{ theme_option('product_option_3', 'Tư vấn tổng quan') }}">{{ theme_option('product_option_3', 'Tư vấn tổng quan') }}</option>
                                </select>
                            </div>

                            <div class="text-center">
                                <button type="submit" class="btn btn-light btn-lg me-3 mb-3">
                                    <i class="fas fa-paper-plane"></i> {{ theme_option('form_submit_text', 'ĐĂNG KÝ NHẬN THÔNG TIN') }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer-new">
        <div class="footer-content">
            <div class="container">
                <!-- Logo Section -->
                <div class="footer-logos text-center mb-4">
                    <div class="logo-group">
                        <div class="logo-item">
                            @if (theme_option('footer_logo_1'))
                                <img src="{{ RvMedia::getImageUrl(theme_option('footer_logo_1')) }}" alt="Green City Logo" class="footer-logo-img">
                            @else
                                <img src="{{ Theme::asset()->url('images/green-city-logo-2.png') }}" alt="Green City Logo" class="footer-logo-img">
                            @endif
                        </div>
                        <div class="logo-separator">
                            <div class="separator-line"></div>
                        </div>
                        <div class="logo-item">
                            @if (theme_option('footer_logo_2'))
                                <img src="{{ RvMedia::getImageUrl(theme_option('footer_logo_2')) }}" alt="Green City Logo 2" class="footer-logo-img">
                            @else
                                <img src="{{ Theme::asset()->url('images/logo.png') }}" alt="Green City Logo 2" class="footer-logo-img">
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Description -->
                <div class="footer-description text-center mb-4">
                    <p>{{ theme_option('footer_description', 'Green City Bình Dương - Khu đô thị chất lượng cao với môi trường sống tiện nghi hiện đại và không gian xanh đẳng cấp Singapore.') }}</p>
                    <h4 class="footer-title">{{ theme_option('footer_title', 'BOOKING NGAY HÔM NAY - NHẬN NGAY ƯU TIÊN VỊ TRÍ.') }}</h4>
                </div>

                <!-- Contact Info -->
                <div class="footer-contact-elegant">
                    <div class="row text-center align-items-center">
                        <div class="col-md-4">
                            <div class="contact-elegant">
                                <i class="fas fa-map-marker-alt contact-elegant-icon"></i>
                                <p>{{ theme_option('footer_address', 'Thành phố mới Bình Dương, tỉnh Bình Dương') }}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="contact-elegant phone-highlight">
                                <i class="fas fa-phone contact-elegant-icon"></i>
                                <a href="tel:{{ theme_option('contact_phone', '0708808891') }}" class="phone-link">
                                    <h4>{{ theme_option('contact_phone', '0708808891') }}</h4>
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="contact-elegant">
                                <i class="fas fa-globe contact-elegant-icon"></i>
                                {{-- <div class="social-links-elegant">
                                    <a href="#" class="social-elegant"><i class="fab fa-facebook"></i></a>
                                    <a href="#" class="social-elegant"><i class="fab fa-youtube"></i></a>
                                    <a href="#" class="social-elegant"><i class="fab fa-zalo"></i></a>
                                </div> --}}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Copyright -->
                <div class="footer-bottom text-center mt-4">
                    <p>© {{ date('Y') }} {{ theme_option('footer_copyright', 'Green City BD. Bản quyền của Công ty TNHH Đầu Tư và Phát Triển Đô Thị Xanh BD.') }}</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button id="backToTop" class="btn btn-primary back-to-top">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Floating Call Button -->
    <a href="tel:{{ theme_option('contact_phone', '0708808891') }}" class="floating-call-btn" id="floatingCallBtn">
        <i class="fas fa-phone"></i>
        <span class="call-text">{{ theme_option('floating_call_text', 'Gọi ngay') }}</span>
    </a>

    <!-- Floating Zalo Button -->
    <a href="https://zalo.me/{{ theme_option('zalo_phone', '0708808891') }}" class="floating-zalo-btn" id="floatingZaloBtn" target="_blank">
        <img src="{{ Theme::asset()->url('images/zalo.svg') }}" alt="Zalo" class="zalo-icon">
        <span class="zalo-text">{{ theme_option('zalo_text', 'Chat Zalo') }}</span>
    </a>

    <!-- Contact Modal -->
    <div id="contactModal" class="contact-modal" style="display: none;">
        <div class="contact-modal-overlay"></div>
        <div class="contact-modal-content">
            <div class="contact-modal-header">
                <h4 class="contact-modal-title">{{ theme_option('modal_contact_title', 'ĐĂNG KÝ NHẬN THÔNG TIN') }}</h4>
                <button type="button" class="contact-modal-close" id="closeContactModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="contact-modal-body">
                <form id="modalContactForm" action="{{ route('public.send.contact') }}" method="POST">
                    @csrf
                    <input type="hidden" name="agree_terms_and_policy" value="1">

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <input type="text" class="form-control" name="name" placeholder="{{ theme_option('form_name_placeholder', 'Họ và tên *') }}" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <input type="text" class="form-control" name="phone" placeholder="{{ theme_option('form_phone_placeholder', 'Số điện thoại *') }}" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <input type="email" class="form-control" name="email_display" placeholder="{{ theme_option('form_email_placeholder', 'Email (tùy chọn)') }}" value="<EMAIL>">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">{{ theme_option('form_product_select_label', 'Loại sản phẩm quan tâm:') }} <span class="text-danger">*</span></label>
                        <select class="form-control" name="content" required>
                            <option value="">{{ theme_option('form_product_select_placeholder', 'Chọn loại sản phẩm') }}</option>
                            <option value="{{ theme_option('product_option_1', 'Liền kề') }}" selected>{{ theme_option('product_option_1', 'Liền kề') }}</option>
                            <option value="{{ theme_option('product_option_2', 'Shophouse') }}">{{ theme_option('product_option_2', 'Shophouse') }}</option>
                            <option value="{{ theme_option('product_option_3', 'Tư vấn tổng quan') }}">{{ theme_option('product_option_3', 'Tư vấn tổng quan') }}</option>
                        </select>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-paper-plane"></i> {{ theme_option('form_submit_text', 'ĐĂNG KÝ NHẬN THÔNG TIN') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Success Popup Modal -->
    <div id="successModal" class="success-modal" style="display: none;">
        <div class="success-modal-overlay"></div>
        <div class="success-modal-content">
            <div class="success-modal-header">
                <div class="success-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h3 class="success-title">{{ theme_option('success_popup_title', 'Cảm ơn bạn!') }}</h3>
            </div>
            <div class="success-modal-body">
                <p class="success-message">{{ theme_option('success_popup_message', 'Thông tin đã được gửi thành công.') }}</p>
                <p class="success-sub-message">{{ theme_option('success_popup_sub_message', 'Chúng tôi sẽ liên hệ với bạn trong thời gian sớm nhất.') }}</p>
            </div>
            <div class="success-modal-footer">
                <button class="success-close-btn" onclick="closeSuccessModal()">
                    <i class="fas fa-times"></i> {{ theme_option('popup_close_text', 'Đóng') }}
                </button>
            </div>
        </div>
    </div>

    <!-- Error Popup Modal -->
    <div id="errorModal" class="error-modal" style="display: none;">
        <div class="error-modal-overlay"></div>
        <div class="error-modal-content">
            <div class="error-modal-header">
                <div class="error-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h3 class="error-title">{{ theme_option('error_popup_title', 'Có lỗi xảy ra!') }}</h3>
            </div>
            <div class="error-modal-body">
                <p class="error-message" id="errorMessage">Vui lòng thử lại sau.</p>
            </div>
            <div class="error-modal-footer">
                <button class="error-close-btn" onclick="closeErrorModal()">
                    <i class="fas fa-times"></i> {{ theme_option('popup_close_text', 'Đóng') }}
                </button>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>

    <!-- AOS Animation -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <!-- YouTube API -->
    <script src="https://www.youtube.com/iframe_api"></script>

    <!-- Fancybox JS - Sử dụng phiên bản 4 thay vì 5 -->
    <script src="https://cdn.jsdelivr.net/npm/@fancyapps/ui@4.0/dist/fancybox.umd.js"></script>
    {!! Theme::footer() !!}
    <!-- Custom JS -->
    <script src="{{ Theme::asset()->url('js/script.js?v=3.1.0') }}"></script>

    <script>
        // Cấu hình Fancybox - Phiên bản đơn giản hơn
        document.addEventListener('DOMContentLoaded', function() {
            // Khởi tạo Fancybox với cấu hình đơn giản
            Fancybox.bind('[data-fancybox]', {
                buttons: [
                    "zoom",
                    "slideShow",
                    "fullScreen",
                    "close"
                ],
                loop: true,
                animationEffect: "fade",
                transitionEffect: "fade",
                // Tiếng Việt
                i18n: {
                    en: {
                        CLOSE: "Đóng",
                        NEXT: "Tiếp",
                        PREV: "Trước",
                        ERROR: "Không thể tải ảnh. <br> Vui lòng thử lại sau.",
                        PLAY_START: "Bắt đầu trình chiếu",
                        PLAY_STOP: "Dừng trình chiếu",
                        FULL_SCREEN: "Toàn màn hình",
                        ZOOM: "Phóng to"
                    }
                }
            });

            // Xử lý form contact
            const contactForm = document.getElementById('contactForm');
            if (contactForm) {
                contactForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const formData = new FormData(this);
                    const submitButton = this.querySelector('button[type="submit"]');
                    const originalText = submitButton.innerHTML;

                    // Cập nhật email từ trường email_display nếu có
                    const emailDisplay = this.querySelector('input[name="email_display"]');
                    if (emailDisplay && emailDisplay.value) {
                        formData.set('email', emailDisplay.value);
                    }

                    // Disable button và hiển thị loading
                    submitButton.disabled = true;
                    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang gửi...';

                    fetch(this.action, {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest'
                            }
                        })
                        .then(response => {
                            // Kiểm tra HTTP status code
                            if (!response.ok) {
                                throw new Error(`HTTP error! status: ${response.status}`);
                            }
                            return response.json();
                        })
                        .then(data => {
                            // Kiểm tra response data
                            if (data.error === true || data.error === 1) {
                                showErrorModal(data.message || 'Có lỗi xảy ra khi gửi form. Vui lòng thử lại.');
                            } else if (data.error === false || data.error === 0 || data.success === true) {
                                showSuccessModal();
                                contactForm.reset();
                            } else {
                                // Trường hợp không xác định được trạng thái
                                showErrorModal('Không thể xác định trạng thái gửi form. Vui lòng thử lại.');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            showErrorModal('Có lỗi xảy ra khi gửi form. Vui lòng thử lại.');
                        })
                        .finally(() => {
                            // Enable button và khôi phục text
                            submitButton.disabled = false;
                            submitButton.innerHTML = originalText;
                        });
                });
            }

            // Contact Modal Scroll Handler
            let contactModalShown = false;
            const contactModal = document.getElementById('contactModal');

            function showContactModal() {
                if (!contactModalShown) {
                    contactModal.style.display = 'flex';
                    document.body.style.overflow = 'hidden';
                    contactModalShown = true;
                }
            }

            function closeContactModal() {
                contactModal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }

            // Show modal when scroll 30%
            window.addEventListener('scroll', function() {
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                const documentHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight;
                const scrollPercent = (scrollTop / documentHeight) * 100;

                if (scrollPercent >= 30 && !contactModalShown) {
                    showContactModal();
                }
            });

            // Close modal events
            document.getElementById('closeContactModal').addEventListener('click', closeContactModal);

            // Handle modal contact form submission
            const modalContactForm = document.getElementById('modalContactForm');
            if (modalContactForm) {
                modalContactForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const formData = new FormData(this);
                    const submitButton = this.querySelector('button[type="submit"]');
                    const originalText = submitButton.innerHTML;

                    // Cập nhật email từ trường email_display nếu có
                    const emailDisplay = this.querySelector('input[name="email_display"]');
                    if (emailDisplay && emailDisplay.value) {
                        formData.set('email', emailDisplay.value);
                    }

                    // Disable button và hiển thị loading
                    submitButton.disabled = true;
                    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang gửi...';

                    fetch(this.action, {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest'
                            }
                        })
                        .then(response => {
                            if (!response.ok) {
                                throw new Error(`HTTP error! status: ${response.status}`);
                            }
                            return response.json();
                        })
                        .then(data => {
                            if (data.error === true || data.error === 1) {
                                showErrorModal(data.message || 'Có lỗi xảy ra khi gửi form. Vui lòng thử lại.');
                            } else if (data.error === false || data.error === 0 || data.success === true) {
                                closeContactModal();
                                showSuccessModal();
                                modalContactForm.reset();
                            } else {
                                showErrorModal('Không thể xác định trạng thái gửi form. Vui lòng thử lại.');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            showErrorModal('Có lỗi xảy ra khi gửi form. Vui lòng thử lại.');
                        })
                        .finally(() => {
                            submitButton.disabled = false;
                            submitButton.innerHTML = originalText;
                        });
                });
            }
        });

        // Popup Modal Functions
        function showSuccessModal() {
            const modal = document.getElementById('successModal');
            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';

            // Auto close after 5 seconds
            setTimeout(() => {
                closeSuccessModal();
            }, 5000);
        }

        function closeSuccessModal() {
            const modal = document.getElementById('successModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function showErrorModal(message) {
            const modal = document.getElementById('errorModal');
            const errorMessage = document.getElementById('errorMessage');
            errorMessage.textContent = message;
            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        function closeErrorModal() {
            const modal = document.getElementById('errorModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // Close modal when clicking on overlay
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('success-modal-overlay')) {
                closeSuccessModal();
            }
            if (e.target.classList.contains('error-modal-overlay')) {
                closeErrorModal();
            }
            if (e.target.classList.contains('contact-modal-overlay')) {
                closeContactModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeSuccessModal();
                closeErrorModal();
                closeContactModal();
            }
        });
    </script>
</body>

</html>

/* Green City Landing Page Styles - Premium Version 2.0 */

/* Font Import */
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700;800;900&display=swap');

/* CSS Variables - Premium Color Palette */
:root {
    /* Primary Colors - Royal Blue Tones */
    --primary-color: #1e3799;
    --primary-light: #3742fa;
    --primary-dark: #152a6b;
    --secondary-color: #5352ed;
    --secondary-light: #7bed9f;
    --secondary-dark: #3742fa;

    /* Accent Colors */
    --accent-emerald: #2ed573;
    --accent-gold: #ffa502;
    --accent-platinum: #e5e7eb;
    --accent-rose: #ff3838;

    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #fafafa;
    --gray-100: #f5f5f5;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* Background Gradients */
    --gradient-primary: linear-gradient(135deg, #1e3799 0%, #3742fa 100%);
    --gradient-secondary: linear-gradient(135deg, #5352ed 0%, #7bed9f 100%);
    --gradient-hero: linear-gradient(135deg, rgba(30, 55, 153, 0.9) 0%, rgba(55, 66, 250, 0.8) 100%);
    --gradient-card: linear-gradient(145deg, #ffffff 0%, #f9fafb 100%);
    --gradient-luxury: linear-gradient(135deg, #1e3799 0%, #5352ed 50%, #7bed9f 100%);

    /* Typography */
    --font-primary: 'Roboto', sans-serif;
    --font-secondary: 'Roboto', sans-serif;

    /* Spacing */
    --border-radius: 16px;
    --border-radius-lg: 24px;
    --border-radius-xl: 32px;

    /* Shadows */
    --shadow-sm: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-luxury: 0 25px 50px -12px rgba(30, 55, 153, 0.25);

    /* Transitions */
    --transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-secondary);
    line-height: 1.7;
    color: var(--gray-700);
    background: var(--white);
    overflow-x: hidden;
    scroll-behavior: smooth;
}

html {
    scroll-behavior: smooth;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: var(--font-primary);
    font-weight: 700;
    line-height: 1.2;
    color: var(--gray-900);
    margin-bottom: 1.5rem;
}

.section-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    color: #1e3799;
    text-align: center;
    margin-bottom: 1.5rem;
    position: relative;
    line-height: 1.1;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 4px;
    background: #05506b;
    border-radius: 2px;
    box-shadow: var(--shadow-md);
}

.section-subtitle {
    font-size: clamp(1.1rem, 2.5vw, 1.4rem);
    color: var(--gray-600);
    text-align: center;
    margin-bottom: 4rem;
    font-weight: 400;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Header Styles */
.header {
    background: rgba(30, 55, 153, 0.9);
    backdrop-filter: blur(15px);
    transition: var(--transition);
    z-index: 1000;
    padding: 1rem 0;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.header.scrolled {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-md);
    padding: 0.5rem 0;
}

.navbar-brand img {
    height: 55px;
    transition: var(--transition);
    filter: brightness(0) invert(1);
}

.header.scrolled .navbar-brand img {
    filter: none;
}

.navbar-nav .nav-link {
    color: var(--white) !important;
    font-weight: 600;
    font-size: 0.95rem;
    padding: 0.75rem 1.5rem !important;
    transition: var(--transition);
    position: relative;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.navbar-nav .nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 3px;
    background: var(--gradient-secondary);
    transition: var(--transition);
    border-radius: 2px;
}

.navbar-nav .nav-link:hover::before {
    width: 80%;
}

.navbar-nav .nav-link:hover {
    color: var(--secondary-color) !important;
}

.header.scrolled .navbar-nav .nav-link {
    color: var(--gray-700) !important;
}

.header.scrolled .navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
}

/* Navbar Toggler Styles */
.navbar-toggler {
    border: none;
    padding: 0.5rem;
    background: transparent;
    position: relative;
    z-index: 1001;
}

.navbar-toggler:focus {
    box-shadow: none;
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
    width: 1.5rem;
    height: 1.5rem;
}

/* Scrolled navbar toggler icon (dark) */
.header.scrolled .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2810, 93, 60, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Navbar toggler button hover effects */
.navbar-toggler:hover {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
}

.header.scrolled .navbar-toggler:hover {
    background: rgba(30, 55, 153, 0.1);
}

/* Mobile navbar collapse styling */
@media (max-width: 991.98px) {
    .navbar-collapse {
        background: rgba(30, 55, 153, 0.95);
        backdrop-filter: blur(15px);
        border-radius: var(--border-radius);
        margin-top: 1rem;
        padding: 1rem;
        box-shadow: var(--shadow-lg);
    }

    .header.scrolled .navbar-collapse {
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(20px);
    }

    .header.scrolled .navbar-collapse .nav-link {
        color: var(--gray-700) !important;
    }

    .header.scrolled .navbar-collapse .nav-link:hover {
        color: var(--primary-color) !important;
    }

    /* Mobile call button styling */
    .navbar .btn-primary {
        margin-top: 1rem;
        width: 100%;
    }

    .header.scrolled .navbar .btn-primary {
        background: var(--gradient-secondary);
        border-color: var(--primary-color);
        color: var(--white) !important;
    }
}

/* Hero Section */
.hero-section {
    position: relative;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-video-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.hero-video {
    position: absolute;
    top: 50%;
    left: 50%;
    min-width: 120%;
    min-height: 120%;
    width: 120vw;
    height: 67.5vw;
    transform: translate(-50%, -50%);
    object-fit: cover;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-hero);
    z-index: 2;
}

.hero-content-wrapper {
    position: relative;
    z-index: 3;
    width: 100%;
}

.hero-content {
    color: var(--white);
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.15);
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin-bottom: 2rem;
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.hero-badge i {
    color: var(--secondary-color);
    font-size: 1.1rem;
}

.hero-title {
    font-size: clamp(2.5rem, 6vw, 5rem);
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: 2rem;
    font-family: var(--font-primary);
}

.title-highlight {
    display: block;
    background: var(--gradient-secondary);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 1.2em;
}

.title-main {
    display: block;
    font-size: 1.5em;
    font-weight: 900;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.title-subtitle {
    display: block;
    font-size: 0.4em;
    font-weight: 500;
    color: var(--secondary-light);
    font-family: var(--font-secondary);
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-top: 1rem;
}

.hero-description {
    font-size: clamp(1.1rem, 2.5vw, 1.3rem);
    line-height: 1.6;
    margin-bottom: 3rem;
    opacity: 0.95;
    max-width: 500px;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    min-width: 120px;
    transition: var(--transition);
}

.stat-item:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 900;
    color: var(--secondary-color);
    font-family: var(--font-primary);
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
    margin-top: 0.5rem;
}

.hero-actions {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.hero-features {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.feature-card {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
    color: var(--white);
}

.feature-card:hover {
    transform: translateX(-10px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: var(--shadow-xl);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-secondary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--white);
    box-shadow: var(--shadow-md);
    flex-shrink: 0;
}

.feature-content h4 {
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
    color: var(--white);
}

.feature-content p {
    margin: 0;
    opacity: 0.9;
    font-size: 0.95rem;
}

/* Floating Elements */
.hero-floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.floating-shape {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(83, 82, 237, 0.2) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 200px;
    height: 200px;
    top: 20%;
    right: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 150px;
    height: 150px;
    bottom: 30%;
    left: 15%;
    animation-delay: 2s;
}

.shape-3 {
    width: 100px;
    height: 100px;
    top: 60%;
    right: 25%;
    animation-delay: 4s;
}

@keyframes float {
    0%,
    100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 3rem;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    color: var(--white);
    z-index: 4;
    cursor: pointer;
    transition: var(--transition);
}

.scroll-indicator:hover {
    transform: translateX(-50%) translateY(-5px);
}

.scroll-text {
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.scroll-arrow {
    font-size: 1.5rem;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%,
    20%,
    50%,
    80%,
    100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Button Styles */
.btn-primary,
.btn-gradient {
    background: var(--gradient-secondary);
    border: none;
    color: var(--white);
    font-weight: 700;
    padding: 1rem 2.5rem;
    border-radius: var(--border-radius-lg);
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: var(--transition);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.btn-primary::before,
.btn-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s;
}

.btn-primary:hover,
.btn-gradient:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
    color: var(--white);
}

.btn-primary:hover::before,
.btn-gradient:hover::before {
    left: 100%;
}

.btn-outline-light {
    background: transparent;
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: var(--white);
    font-weight: 600;
    padding: 1rem 2.5rem;
    border-radius: var(--border-radius-lg);
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.btn-outline-light:hover {
    background: var(--white);
    color: var(--primary-color);
    border-color: var(--white);
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

/* Section Styles */
.section {
    padding: 8rem 0;
    position: relative;
}

.section.bg-light {
    background: linear-gradient(135deg, #fafafa 0%, #f3f4f6 100%);
}

/* Introduction Section */
.introduction-section {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    position: relative;
    overflow: hidden;
}

.introduction-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 20%, rgba(30, 55, 153, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(83, 82, 237, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

.intro-card-wrapper {
    position: relative;
    z-index: 2;
}

.intro-main-card {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-xl);
    padding: 4rem;
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;
}

.intro-main-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 6px;
    background: var(--gradient-secondary);
}

.intro-content {
    text-align: center;
    margin-bottom: 4rem;
}

.intro-icon-large {
    width: 120px;
    height: 120px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    box-shadow: var(--shadow-luxury);
}

.intro-icon-large i {
    font-size: 3rem;
    color: var(--white);
}

.intro-content h3 {
    color: var(--primary-color);
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    font-family: var(--font-primary);
}

.intro-content p {
    font-size: 1.2rem;
    line-height: 1.8;
    color: var(--gray-700);
    max-width: 800px;
    margin: 0 auto;
}

.intro-features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.intro-feature-item {
    background: var(--gradient-card);
    padding: 2.5rem;
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--gray-200);
    text-align: center;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.intro-feature-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-secondary);
    transform: scaleX(0);
    transition: var(--transition);
}

.intro-feature-item:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.intro-feature-item:hover::before {
    transform: scaleX(1);
}

.feature-number {
    width: 60px;
    height: 60px;
    background: var(--gradient-secondary);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 900;
    margin: 0 auto 1.5rem;
    font-family: var(--font-primary);
    box-shadow: var(--shadow-md);
}

.intro-feature-item h4 {
    color: var(--primary-color);
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 1rem;
    font-family: var(--font-primary);
}

.intro-feature-item p {
    color: var(--gray-700);
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 0;
}

/* Tổng Quan Section */
.tong-quan-section {
    position: relative;
    overflow: hidden;
    background: var(--gray-900);
}

.tong-quan-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../images/green-city-binh-duong-2.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    z-index: 1;
}

.tong-quan-section .container-fluid {
    position: relative;
    z-index: 3;
}

.tong-quan-section .section-title {
    color: var(--white) !important;
    text-align: center;
    padding-top: 2rem;
    margin-top: 2rem;
    line-height: 1.2;
    transition: var(--transition);
    position: relative;
}

.tong-quan-section .section-title::after {
    background: var(--white);
    left: 50%;
    right: auto;
    transform: translateX(-50%);
}

.tong-quan-section .section-title:hover {
    transform: scale(1.05);
    text-shadow: 0 0 30px rgba(255, 255, 255, 0.8);
}

/* Project Info Compact Styles */
.project-info-compact {
    max-width: 900px;
    margin-left: auto;
    position: relative;
}

/* .project-info-compact::before {
  content: "";
  position: absolute;
  top: -20px;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-secondary);
  border-radius: 2px;
  box-shadow: 0 0 20px rgba(201, 169, 110, 0.5);
} */

.info-group {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(25px);
    border-radius: var(--border-radius-xl);
    padding: 2.5rem;
    border: 1px solid rgba(255, 255, 255, 0.8);
    position: relative;
    overflow: hidden;
    transition: var(--transition);
    margin-bottom: 2rem;
    box-shadow: 0 8px 32px rgba(30, 55, 153, 0.3);
}

.info-group::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(83, 82, 237, 0.1), transparent);
    transition: left 0.8s ease;
}

.info-group:hover::before {
    left: 100%;
}

.info-group:hover {
    transform: translateY(-10px) scale(1.02);
    background: rgba(255, 255, 255, 0.75);
    border-color: rgba(83, 82, 237, 0.8);
    box-shadow:
        0 25px 50px rgba(30, 55, 153, 0.4),
        0 0 30px rgba(83, 82, 237, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.8),
        0 0 60px rgba(83, 82, 237, 0.2);
}

.info-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.2rem 1.5rem;
    border-bottom: 1px solid rgba(30, 55, 153, 0.1);
    position: relative;
    transition: var(--transition);
    overflow: hidden;
    border-radius: var(--border-radius);
    margin-bottom: 0.5rem;
}

.info-item:last-child {
    border-bottom: none;
}

.info-item::before {
    content: '';
    position: absolute;
    left: -100%;
    top: 0;
    width: 100%;
    height: 2px;
    background: var(--gradient-secondary);
    transition: left 0.6s ease;
    box-shadow: 0 0 10px rgba(83, 82, 237, 0.6);
}

.info-item:hover::before {
    left: 100%;
}

.info-item:hover {
    transform: translateX(10px);
    background: rgba(83, 82, 237, 0.15);
    border-bottom-color: rgba(83, 82, 237, 0.3);
    box-shadow: inset 0 0 20px rgba(83, 82, 237, 0.1);
}

.info-item strong {
    color: #0a5d3c;
    font-weight: 700;
    font-size: 1.1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    transition: var(--transition);
    text-shadow: none;
}

.info-item:hover strong {
    color: #b8860b;
    text-shadow: 0 2px 4px rgba(184, 134, 11, 0.4);
    transform: scale(1.05);
    letter-spacing: 1px;
}

.info-item span {
    color: #374151;
    font-weight: 600;
    font-size: 1.1rem;
    transition: var(--transition);
    position: relative;
    text-shadow: none;
}

.info-item:hover span {
    color: #1e3799;
    text-shadow: 0 1px 2px rgba(30, 55, 153, 0.2);
    transform: scale(1.02);
    font-weight: 700;
}

/* Staggered Animation Effect */
.info-group:nth-child(1) .info-item:nth-child(1) {
    transition-delay: 0.1s;
}
.info-group:nth-child(1) .info-item:nth-child(2) {
    transition-delay: 0.15s;
}
.info-group:nth-child(1) .info-item:nth-child(3) {
    transition-delay: 0.2s;
}
.info-group:nth-child(1) .info-item:nth-child(4) {
    transition-delay: 0.25s;
}
.info-group:nth-child(1) .info-item:nth-child(5) {
    transition-delay: 0.3s;
}
.info-group:nth-child(1) .info-item:nth-child(6) {
    transition-delay: 0.35s;
}

.info-group:nth-child(2) .info-item:nth-child(1) {
    transition-delay: 0.4s;
}
.info-group:nth-child(2) .info-item:nth-child(2) {
    transition-delay: 0.45s;
}
.info-group:nth-child(2) .info-item:nth-child(3) {
    transition-delay: 0.5s;
}
.info-group:nth-child(2) .info-item:nth-child(4) {
    transition-delay: 0.55s;
}
.info-group:nth-child(2) .info-item:nth-child(5) {
    transition-delay: 0.6s;
}
.info-group:nth-child(2) .info-item:nth-child(6) {
    transition-delay: 0.65s;
}

/* Floating particles effect */
.info-group::after {
    content: '';
    position: absolute;
    top: 20%;
    right: 10%;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(83, 82, 237, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    opacity: 0;
    transition: var(--transition);
    animation: float 4s ease-in-out infinite;
}

.info-group:hover::after {
    opacity: 1;
    transform: scale(1.5);
}

/* Glowing border effect */
.project-info-compact:hover .info-group {
    border-color: rgba(83, 82, 237, 0.3);
    box-shadow:
        0 0 20px rgba(30, 55, 153, 0.2),
        0 0 40px rgba(83, 82, 237, 0.1);
}

/* Enhanced floating animation */
@keyframes float {
    0%,
    100% {
        transform: translateY(0px) rotate(0deg);
    }
    33% {
        transform: translateY(-10px) rotate(120deg);
    }
    66% {
        transform: translateY(5px) rotate(240deg);
    }
}

/* Location Section Improvements */
.location-content {
    background: var(--gradient-card);
    padding: 3rem;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.location-content p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 1.5rem;
    color: var(--gray-700);
}

.location-content strong {
    color: var(--primary-color);
    font-weight: 700;
}

.location-image img {
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-xl);
    transition: var(--transition);
}

.location-image:hover img {
    transform: scale(1.02);
}

/* Liên kết vùng Section */
.lien-ket-vung-section {
    position: relative;
    overflow: hidden;
    background: var(--primary-color);
}

.lien-ket-vung-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../images/banner-cdt-becamex.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    z-index: 1;
}

.lien-ket-vung-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        rgba(30, 55, 153, 0.85) 0%,
        rgba(21, 42, 107, 0.9) 50%,
        rgba(30, 55, 153, 0.85) 100%
    );
    z-index: 2;
}

.lien-ket-vung-section .container {
    position: relative;
    z-index: 3;
}

.lien-ket-vung-section .section-title {
    color: var(--white) !important;
}

.lien-ket-vung-section .section-title::after {
    background: var(--white);
}

.lien-ket-vung-content {
    background: rgba(255, 255, 255, 0.95);
    padding: 3rem;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-xl);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.lien-ket-vung-content p {
    color: var(--gray-700);
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 2rem;
}

.lien-ket-vung-content h4 {
    color: var(--primary-color);
    font-weight: 700;
    margin-bottom: 2rem;
    font-size: 1.6rem;
    font-family: var(--font-primary);
}

.strategic-connections {
    list-style: none;
    padding: 0;
    margin: 0;
}

.strategic-connections li {
    position: relative;
    padding: 1.5rem 0 1.5rem 3rem;
    border-left: 3px solid var(--accent-emerald);
    margin-bottom: 1.5rem;
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
}

.strategic-connections li::before {
    content: '🌟';
    position: absolute;
    left: -12px;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    box-shadow: var(--shadow-md);
}

.strategic-connections li:hover {
    transform: translateX(10px);
    box-shadow: var(--shadow-lg);
    border-left-color: var(--primary-color);
}

.strategic-connections li strong {
    color: var(--primary-color);
    font-weight: 700;
}

/* Map container styles */
.lien-ket-vung-map {
    position: relative;
    background: rgba(255, 255, 255, 0.95);
    padding: 2rem;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-xl);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
}

.lien-ket-vung-map:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-luxury);
}

.lien-ket-vung-map img {
    width: 100%;
    height: auto;
    border-radius: var(--border-radius-lg);
    transition: var(--transition);
    box-shadow: var(--shadow-md);
}

.lien-ket-vung-map:hover img {
    transform: scale(1.02);
    box-shadow: var(--shadow-lg);
}

.map-caption {
    margin-top: 2rem;
    text-align: center;
    padding-top: 1.5rem;
    border-top: 2px solid var(--gray-200);
}

.map-caption h5 {
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1.4rem;
    margin-bottom: 0.75rem;
    font-family: var(--font-primary);
}

.map-caption p {
    color: var(--gray-600);
    font-size: 1rem;
    line-height: 1.6;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content {
        text-align: center;
    }

    .hero-stats {
        justify-content: center;
    }

    .hero-actions {
        justify-content: center;
    }

    .hero-actions .btn {
        width: 100%;
        margin-bottom: 1rem;
    }

    .hero-features {
        margin-top: 3rem;
    }

    .feature-card {
        flex-direction: column;
        text-align: center;
    }

    .section {
        padding: 4rem 0;
    }

    .section-title {
        font-size: 2.5rem;
    }

    .tong-quan-section .section-title {
        text-align: center;
    }

    .tong-quan-section .section-title::after {
        left: 50%;
        right: auto;
        transform: translateX(-50%);
    }

    .project-info-compact {
        max-width: 100%;
    }

    .info-group {
        padding: 2rem;
        margin-bottom: 1.5rem;
    }

    .info-item {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
        padding: 1rem 0;
    }

    .info-item:hover {
        transform: translateY(-5px);
        padding-left: 0;
    }

    .info-item strong,
    .info-item span {
        text-align: center;
    }
}

@media (max-width: 576px) {
    .hero-section {
        padding: 0 1rem;
    }

    .hero-badge {
        font-size: 0.8rem;
        padding: 0.5rem 1rem;
    }

    .stat-item {
        min-width: 100px;
        padding: 1rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .hero-floating-elements {
        display: none;
    }

    .location-content {
        padding: 2rem;
    }

    .lien-ket-vung-content {
        padding: 2rem;
    }

    .lien-ket-vung-map {
        padding: 1.5rem;
        margin-top: 2rem;
    }

    .map-caption {
        margin-top: 1.5rem;
        padding-top: 1rem;
    }

    .map-caption h5 {
        font-size: 1.2rem;
    }
}

/* Amenities Styles */
.amenities-title {
    color: var(--primary-color);
    font-size: 2rem;
    margin-bottom: 3rem;
    font-family: var(--font-primary);
    position: relative;
}

.amenities-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 60px;
    height: 3px;
    background: var(--gradient-secondary);
    border-radius: 2px;
}

/* New Amenity Card Styles */
.amenity-card {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    border: 1px solid var(--gray-200);
    height: 100%;
    position: relative;
}

.amenity-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.amenity-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.amenity-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.amenity-card:hover .amenity-image img {
    transform: scale(1.1);
}

.amenity-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(30, 55, 153, 0.7) 0%, rgba(55, 66, 250, 0.7) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.amenity-card:hover .amenity-overlay {
    opacity: 1;
}

.amenity-overlay i {
    font-size: 3rem;
    color: var(--white);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    animation: bounce-gentle 2s infinite;
}

@keyframes bounce-gentle {
    0%,
    100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

.amenity-content {
    padding: 1.5rem;
    text-align: center;
    background: linear-gradient(135deg, var(--white) 0%, var(--gray-50) 100%);
}

.amenity-content h5 {
    color: var(--primary-color);
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    font-family: var(--font-primary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.amenity-content p {
    color: var(--gray-600);
    font-size: 0.95rem;
    line-height: 1.5;
    margin: 0;
    font-weight: 500;
}

.amenity-item {
    display: flex;
    align-items: center;
    background: var(--gradient-card);
    padding: 2.5rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    height: 100%;
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;
}

.amenity-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background: var(--gradient-primary);
    opacity: 0;
    transition: var(--transition);
}

.amenity-item:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.amenity-item:hover::before {
    opacity: 1;
}

.amenity-item i {
    font-size: 3rem;
    background: var(--gradient-primary);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-right: 2rem;
    min-width: 80px;
}

.amenity-item span {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--gray-700);
    line-height: 1.5;
}

.external-amenity {
    display: flex;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding: 2rem;
    background: var(--gradient-card);
    border-radius: var(--border-radius-lg);
    border-left: 5px solid var(--accent-emerald);
    box-shadow: var(--shadow-md);
    transition: var(--transition);
}

.external-amenity:hover {
    transform: translateX(10px);
    box-shadow: var(--shadow-lg);
    border-left-color: var(--primary-color);
}

.external-amenity i {
    margin-right: 1.5rem;
    margin-top: 0.25rem;
    font-size: 1.5rem;
    color: var(--accent-emerald);
    min-width: 25px;
}

.external-amenity span {
    font-size: 1.1rem;
    color: var(--gray-700);
    line-height: 1.7;
    font-weight: 500;
}

/* Gallery Styles */
.gallery-carousel {
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
    background: var(--white);
    position: relative;
}

.gallery-main-img {
    height: 600px;
    object-fit: cover;
    transition: var(--transition);
}

.gallery-carousel:hover .gallery-main-img {
    transform: scale(1.03);
}

.carousel-caption {
    background: linear-gradient(transparent, rgba(30, 55, 153, 0.9));
    padding: 3rem;
    bottom: 0;
    left: 0;
    right: 0;
    backdrop-filter: blur(10px);
}

.carousel-caption h5 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--white);
    margin-bottom: 1rem;
    font-family: var(--font-primary);
}

.carousel-caption p {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0;
    line-height: 1.6;
}

.carousel-control-prev,
.carousel-control-next {
    width: 80px;
    height: 80px;
    background: var(--gradient-secondary);
    border-radius: 50%;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.9;
    transition: var(--transition);
    border: 3px solid rgba(255, 255, 255, 0.2);
}

.carousel-control-prev {
    left: 30px;
}

.carousel-control-next {
    right: 30px;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
    opacity: 1;
    transform: translateY(-50%) scale(1.1);
    box-shadow: var(--shadow-xl);
}

.thumbnail-gallery {
    margin-top: 3rem;
}

.thumbnail-item {
    position: relative;
    cursor: pointer;
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
    border: 3px solid transparent;
}

.thumbnail-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.thumbnail-item.active {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-luxury);
}

.thumbnail-item img {
    width: 100%;
    height: 100px;
    object-fit: cover;
    transition: var(--transition);
}

/* Amenities Gallery */
.amenities-carousel {
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    background: var(--white);
    margin-bottom: 3rem;
}

.amenities-main-img {
    height: 450px;
    object-fit: cover;
}

.amenities-thumbnail-gallery {
    margin-top: 2rem;
}

.amenities-thumbnail-item {
    position: relative;
    cursor: pointer;
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
    border: 3px solid transparent;
}

.amenities-thumbnail-item:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.amenities-thumbnail-item.active {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-luxury);
}

.amenities-thumbnail-item img {
    width: 100%;
    height: 80px;
    object-fit: cover;
}

/* Developer Section */
.chu-dau-tu-section {
    position: relative;
    overflow: hidden;
    background: var(--gray-900);
}

.chu-dau-tu-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../images/becamex-alaska.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    z-index: 1;
}

.chu-dau-tu-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        rgba(31, 41, 55, 0.88) 0%,
        rgba(17, 24, 39, 0.92) 50%,
        rgba(31, 41, 55, 0.88) 100%
    );
    z-index: 2;
}

.chu-dau-tu-section .container {
    position: relative;
    z-index: 3;
}

.chu-dau-tu-section .section-title {
    color: var(--white) !important;
}

.chu-dau-tu-section .section-title::after {
    background: var(--white);
}

/* Developer Content Compact */
.developer-content-compact {
    background: rgba(255, 255, 255, 0.95);
    padding: 3rem;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-xl);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.developer-main-info {
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 2px solid var(--gray-200);
}

.developer-logo {
    background: var(--white);
    padding: 1.5rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 2px solid var(--gray-200);
    transition: var(--transition);
}

.developer-logo:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.developer-logo img {
    max-height: 80px;
    width: auto;
}

.developer-intro h4 {
    color: var(--primary-color);
    font-weight: 700;
    margin-bottom: 1.5rem;
    font-size: 1.6rem;
    font-family: var(--font-primary);
}

.developer-intro p {
    color: var(--gray-700);
    font-size: 1.1rem;
    line-height: 1.7;
    margin-bottom: 2rem;
}

.developer-highlights {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.highlight-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    background: var(--gray-50);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--accent-emerald);
    transition: var(--transition);
}

.highlight-item:hover {
    background: var(--white);
    border-left-color: var(--primary-color);
    transform: translateX(5px);
    box-shadow: var(--shadow-sm);
}

.highlight-item i {
    color: var(--primary-color);
    font-size: 1.2rem;
    min-width: 20px;
}

.highlight-item span {
    color: var(--gray-700);
    font-weight: 600;
    font-size: 0.95rem;
}

.developer-achievements {
    background: linear-gradient(135deg, var(--gray-50), rgba(10, 93, 60, 0.05));
    padding: 2.5rem;
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--gray-200);
}

.developer-achievements h5 {
    color: var(--primary-color);
    font-weight: 700;
    margin-bottom: 2rem;
    font-size: 1.4rem;
    font-family: var(--font-primary);
    display: flex;
    align-items: center;
}

.achievement-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.achievement-list li {
    position: relative;
    padding: 0.75rem 0 0.75rem 2rem;
    color: var(--gray-700);
    font-weight: 500;
    line-height: 1.6;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: var(--transition);
}

.achievement-list li:last-child {
    border-bottom: none;
}

.achievement-list li::before {
    content: '✓';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    background: var(--gradient-primary);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 700;
    box-shadow: var(--shadow-sm);
}

.achievement-list li:hover {
    color: var(--primary-color);
    padding-left: 2.5rem;
}

.achievement-list li:hover::before {
    background: var(--gradient-secondary);
    transform: translateY(-50%) scale(1.1);
}

.developer-intro-card {
    background: var(--gradient-primary);
    padding: 3rem;
    border-radius: var(--border-radius-lg);
    color: var(--white);
    text-align: center;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-luxury);
}

.intro-icon {
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    box-shadow: var(--shadow-md);
}

.intro-icon i {
    font-size: 2.5rem;
    color: var(--white);
}

.developer-intro-card h5 {
    color: var(--white);
    font-weight: 700;
    margin-bottom: 2rem;
    font-size: 1.6rem;
    font-family: var(--font-primary);
}

.developer-intro-card p {
    color: rgba(255, 255, 255, 0.95);
    line-height: 1.8;
    margin-bottom: 0;
    font-size: 1.2rem;
}

.developer-highlight-card {
    background: var(--white);
    padding: 2.5rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    height: 100%;
    border-left: 5px solid var(--primary-color);
}

.developer-highlight-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-left-color: var(--secondary-color);
}

.highlight-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-secondary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-md);
}

.highlight-icon i {
    font-size: 2rem;
    color: var(--white);
}

.developer-highlight-card h5 {
    color: var(--primary-color);
    font-weight: 700;
    margin-bottom: 1.5rem;
    font-size: 1.4rem;
    font-family: var(--font-primary);
}

.developer-highlight-card p {
    color: var(--gray-700);
    line-height: 1.7;
    margin-bottom: 0;
    font-size: 1.1rem;
}

.developer-summary {
    margin-top: 3rem;
    padding-top: 3rem;
    border-top: 2px solid var(--gray-200);
}

.summary-content {
    text-align: center;
    background: linear-gradient(135deg, var(--gray-50), rgba(10, 93, 60, 0.05));
    padding: 3rem;
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--gray-200);
}

.summary-content h4 {
    color: var(--primary-color);
    font-weight: 700;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    font-family: var(--font-primary);
    font-size: 1.6rem;
}

.summary-content p {
    color: var(--gray-700);
    font-size: 1.2rem;
    line-height: 1.8;
    margin-bottom: 0;
}

/* Other Sections */
.masterplan-content {
    background: var(--gradient-card);
    padding: 3rem;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
}

.masterplan-content p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--gray-700);
    margin-bottom: 2rem;
}

.masterplan-image img {
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    transition: var(--transition);
}

.masterplan-image:hover img {
    transform: scale(1.02);
}

/* Payment Styles */
.payment-info {
    background: var(--gradient-card);
    padding: 3rem;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
}

.payment-info h4 {
    color: var(--primary-color);
    margin-bottom: 2rem;
    font-family: var(--font-primary);
    font-size: 1.8rem;
}

.loan-info {
    margin-bottom: 3rem;
}

.loan-item {
    margin-bottom: 2rem;
    padding: 2rem;
    background: var(--white);
    border-radius: var(--border-radius-lg);
    border-left: 5px solid var(--accent-emerald);
    box-shadow: var(--shadow-sm);
}

.loan-item strong {
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1.1rem;
}

.price-table {
    background: var(--white);
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
}

.table-success th {
    background: var(--gradient-primary) !important;
    color: var(--white);
    border: none !important;
    font-weight: 700;
    padding: 1.5rem !important;
}

.table tbody td {
    padding: 1.2rem !important;
    border-color: var(--gray-200) !important;
    font-weight: 500;
}

/* Mobile Price Blocks */
.price-blocks {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.price-block-item {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    border: 1px solid var(--gray-200);
    transition: var(--transition);
}

.price-block-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.price-block-header {
    background: var(--gradient-primary);
    color: var(--white);
    padding: 1.5rem;
    text-align: center;
}

.price-block-header h5 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: var(--white);
}

.price-block-body {
    padding: 1.5rem;
}

.price-detail {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid var(--gray-200);
    transition: var(--transition);
}

.price-detail:last-child {
    border-bottom: none;
}

.price-detail:hover {
    background: var(--gray-50);
    padding-left: 1rem;
    padding-right: 1rem;
    border-radius: var(--border-radius);
}

.price-detail .label {
    color: var(--gray-600);
    font-weight: 600;
    font-size: 1rem;
}

.price-detail .value {
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1.1rem;
}

/* Responsive adjustments */
@media (max-width: 991px) {
    .price-blocks {
        margin-top: 1rem;
    }

    .price-block-item {
        margin-bottom: 1rem;
    }

    .price-block-header {
        padding: 1.2rem;
    }

    .price-block-header h5 {
        font-size: 1.2rem;
    }

    .price-block-body {
        padding: 1.2rem;
    }

    .price-detail {
        padding: 0.8rem 0;
    }
}

/* Video Styles */
.video-container {
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
    background: var(--white);
    padding: 1rem;
}

/* Contact Form */
.contact-form {
    background: rgba(255, 255, 255, 0.1);
    padding: 3rem;
    border-radius: var(--border-radius-xl);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.contact-form .form-control {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid transparent;
    border-radius: var(--border-radius);
    padding: 1.2rem;
    font-size: 1rem;
    font-weight: 500;
    transition: var(--transition);
}

.contact-form .form-control:focus {
    background: var(--white);
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(201, 169, 110, 0.25);
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-label {
    font-weight: 500;
    font-size: 1rem;
}

/* Footer Styles */
.footer-new {
    background: linear-gradient(135deg, var(--gray-900) 0%, var(--gray-800) 100%);
    position: relative;
    overflow: hidden;
}

.footer-new::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 30%, rgba(201, 169, 110, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(10, 93, 60, 0.1) 0%, transparent 50%);
    z-index: 1;
}

.footer-content {
    position: relative;
    z-index: 2;
    padding: 5rem 0 3rem;
    color: var(--white);
}

.footer-logos {
    margin-bottom: 4rem;
}

.logo-group {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 3rem;
    flex-wrap: wrap;
}

.footer-logo-img {
    height: 70px;
    width: auto;
    filter: brightness(0) invert(1);
    transition: var(--transition);
}

/* Special styling for the second logo to make it bigger */
.logo-item:last-child .footer-logo-img {
    height: 90px;
    filter: none;
}

.footer-logo-img:hover {
    transform: scale(1.1);
}

/* Logo separator styling */
.logo-separator {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 1rem;
}

.separator-line {
    width: 2px;
    height: 60px;
    background: linear-gradient(
        to bottom,
        transparent 0%,
        rgba(255, 255, 255, 0.3) 20%,
        rgba(255, 255, 255, 0.8) 50%,
        rgba(255, 255, 255, 0.3) 80%,
        transparent 100%
    );
    position: relative;
}

.separator-line::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.footer-description {
    max-width: 900px;
    margin: 0 auto;
    text-align: center;
}

.footer-description p {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.7;
    margin-bottom: 2rem;
}

.footer-title {
    font-size: 1.6rem;
    font-weight: 700;
    color: var(--white);
    letter-spacing: 1px;
    margin-bottom: 0;
    font-family: var(--font-primary);
    background: var(--gradient-secondary);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.footer-contact-elegant {
    margin: 4rem 0;
    position: relative;
}

.footer-contact-elegant::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 2px;
    background: var(--gradient-secondary);
    opacity: 0.7;
}

.contact-elegant {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
    padding: 3rem 1rem;
    position: relative;
}

.contact-elegant-icon {
    font-size: 2rem;
    background: var(--gradient-secondary);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 1rem;
}

.contact-elegant p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0;
    font-size: 1.1rem;
    line-height: 1.6;
    text-align: center;
}

.phone-highlight h4 {
    color: var(--white);
    font-size: 2.5rem;
    font-weight: 900;
    margin-bottom: 0;
    letter-spacing: 3px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    font-family: var(--font-primary);
    background: var(--gradient-secondary);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.social-links-elegant {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
}

.social-elegant {
    width: 50px;
    height: 50px;
    background: transparent;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: var(--transition);
    font-size: 1.2rem;
}

.social-elegant:hover {
    border-color: var(--secondary-color);
    color: var(--secondary-color);
    transform: scale(1.2);
    background: rgba(201, 169, 110, 0.1);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 2rem;
}

.footer-bottom p {
    color: rgba(255, 255, 255, 0.6);
    font-size: 1rem;
    margin-bottom: 0;
}

/* Floating Elements */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 60px !important;
    height: 60px !important;
    border-radius: 50% !important;
    background: var(--gradient-secondary) !important;
    border: none !important;
    color: var(--white) !important;
    font-size: 1.5rem;
    cursor: pointer;
    transition: var(--transition);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 !important;
    min-width: auto !important;
    min-height: auto !important;
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    transform: translateY(-5px) scale(1.1);
    box-shadow: var(--shadow-xl);
}

.floating-call-btn {
    position: fixed;
    bottom: 7rem;
    right: 2rem;
    background: linear-gradient(135deg, #ff6b35, #ff8c42);
    color: var(--white);
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    font-size: 1.8rem;
    z-index: 1001;
    transition: var(--transition);
    box-shadow: var(--shadow-xl);
    animation: pulse-call 3s infinite;
}

.floating-call-btn:hover {
    transform: scale(1.2);
    color: var(--white);
    text-decoration: none;
    animation: none;
}

.floating-call-btn .call-text {
    position: absolute;
    right: 80px;
    background: var(--gray-800);
    color: var(--white);
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    font-size: 1rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    box-shadow: var(--shadow-lg);
    font-weight: 600;
}

.floating-call-btn:hover .call-text {
    opacity: 1;
    visibility: visible;
    transform: translateX(-10px);
}

@keyframes pulse-call {
    0% {
        box-shadow:
            0 4px 20px rgba(255, 107, 53, 0.4),
            0 0 0 0 rgba(255, 107, 53, 0.7);
    }
    70% {
        box-shadow:
            0 4px 20px rgba(255, 107, 53, 0.4),
            0 0 0 15px rgba(255, 107, 53, 0);
    }
    100% {
        box-shadow:
            0 4px 20px rgba(255, 107, 53, 0.4),
            0 0 0 0 rgba(255, 107, 53, 0);
    }
}

/* Phone Link */
.phone-link {
    color: inherit;
    text-decoration: none;
    transition: var(--transition);
}

.phone-link:hover {
    text-decoration: none;
    transform: scale(1.05);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .amenity-item {
        flex-direction: column;
        text-align: center;
        padding: 2rem;
    }

    .amenity-item i {
        margin-right: 0;
        margin-bottom: 1rem;
    }

    .external-amenity {
        flex-direction: column;
        text-align: center;
        padding: 1.5rem;
    }

    .external-amenity i {
        margin-right: 0;
        margin-bottom: 1rem;
    }

    /* Amenity Card Mobile Responsive */
    .amenity-image {
        height: 180px;
    }

    .amenity-content {
        padding: 1.2rem;
    }

    .amenity-content h5 {
        font-size: 1.1rem;
    }

    .amenity-content p {
        font-size: 0.9rem;
    }

    .amenity-overlay i {
        font-size: 2.5rem;
    }
}

@media (max-width: 768px) {
    .developer-content-compact {
        padding: 2rem;
    }

    .developer-main-info {
        margin-bottom: 2rem;
        padding-bottom: 1.5rem;
    }

    .developer-logo {
        padding: 1rem;
        margin-bottom: 1.5rem;
    }

    .developer-logo img {
        max-height: 60px;
    }

    .developer-intro h4 {
        font-size: 1.4rem;
        text-align: center;
    }

    .developer-highlights {
        gap: 0.75rem;
    }

    .highlight-item {
        padding: 0.5rem;
        gap: 0.75rem;
    }

    .developer-achievements {
        padding: 2rem;
    }

    .achievement-list li {
        padding: 0.5rem 0 0.5rem 1.5rem;
    }

    .gallery-main-img {
        height: 350px;
    }

    .carousel-caption {
        padding: 2rem;
    }

    .carousel-caption h5 {
        font-size: 1.5rem;
    }

    .carousel-caption p {
        font-size: 1rem;
    }

    .carousel-control-prev,
    .carousel-control-next {
        width: 60px;
        height: 60px;
    }

    .carousel-control-prev {
        left: 15px;
    }

    .carousel-control-next {
        right: 15px;
    }

    .footer-content {
        padding: 3rem 0 2rem;
    }

    .contact-elegant {
        padding: 2rem 1rem;
    }

    .phone-highlight h4 {
        font-size: 2rem;
        letter-spacing: 2px;
    }

    .floating-call-btn {
        bottom: 6rem;
        right: 1.5rem;
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .floating-call-btn .call-text {
        right: 70px;
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }
}

@media (max-width: 576px) {
    .contact-form {
        padding: 2rem;
    }

    .payment-info,
    .masterplan-content {
        padding: 2rem;
    }

    .developer-intro-card {
        padding: 2rem;
    }

    .intro-icon {
        width: 80px;
        height: 80px;
    }

    .intro-icon i {
        font-size: 2rem;
    }
}

/* Utility Classes */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-track {
    background: var(--gray-200);
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-secondary);
    border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* Floating Zalo Button */
.floating-zalo-btn {
    position: fixed;
    bottom: 13rem;
    right: 2rem;
    background: #ffffff;
    color: var(--white);
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    z-index: 1001;
    transition: var(--transition);
    box-shadow: var(--shadow-xl);
    animation: pulse-zalo 3s infinite;
    border: 3px solid #0068ff;
}

.floating-zalo-btn:hover {
    transform: scale(1.2);
    color: var(--white);
    text-decoration: none;
    animation: none;
}

.floating-zalo-btn .zalo-icon {
    width: 40px;
    height: 40px;
}

.floating-zalo-btn .zalo-text {
    position: absolute;
    right: 80px;
    background: var(--gray-800);
    color: var(--white);
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    font-size: 1rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    box-shadow: var(--shadow-lg);
    font-weight: 600;
}

.floating-zalo-btn:hover .zalo-text {
    opacity: 1;
    visibility: visible;
    transform: translateX(-10px);
}

@keyframes pulse-zalo {
    0% {
        box-shadow:
            0 4px 20px rgba(0, 104, 255, 0.4),
            0 0 0 0 rgba(0, 104, 255, 0.7);
    }
    70% {
        box-shadow:
            0 4px 20px rgba(0, 104, 255, 0.4),
            0 0 0 15px rgba(0, 104, 255, 0);
    }
    100% {
        box-shadow:
            0 4px 20px rgba(0, 104, 255, 0.4),
            0 0 0 0 rgba(0, 104, 255, 0);
    }
}

/* Responsive adjustments for Zalo button */
@media (max-width: 768px) {
    .floating-zalo-btn {
        bottom: 11rem;
        right: 1.5rem;
        width: 60px;
        height: 60px;
    }

    .floating-zalo-btn .zalo-icon {
        width: 35px;
        height: 35px;
    }

    .floating-zalo-btn .zalo-text {
        right: 70px;
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }
}

/* Contact Modal */
.contact-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease-out;
}

.contact-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
}

.contact-modal-content {
    position: relative;
    background: var(--white);
    border-radius: 20px;
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: slideInUp 0.4s ease-out;
}

.contact-modal-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    padding: 2rem;
    border-radius: 20px 20px 0 0;
    position: relative;
    text-align: center;
}

.contact-modal-title {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.contact-modal-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: var(--white);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    font-size: 1.2rem;
}

.contact-modal-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.contact-modal-body {
    padding: 2rem;
}

.contact-modal .form-control {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 1rem;
    font-size: 1rem;
    transition: var(--transition);
    background: #f8f9fa;
}

.contact-modal .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    background: var(--white);
}

.contact-modal .form-label {
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
}

.contact-modal .btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
    border-radius: 50px;
    padding: 1rem 2rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: var(--transition);
    width: 100%;
    font-size: 1.1rem;
}

.contact-modal .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(40, 167, 69, 0.4);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Responsive Contact Modal */
@media (max-width: 768px) {
    .contact-modal-content {
        width: 95%;
        margin: 1rem;
    }

    .contact-modal-header {
        padding: 1.5rem;
    }

    .contact-modal-title {
        font-size: 1.2rem;
    }

    .contact-modal-body {
        padding: 1.5rem;
    }

    .contact-modal .form-control {
        padding: 0.8rem;
        font-size: 0.9rem;
    }

    .contact-modal .btn-primary {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
    }
}

// Green City Landing Page JavaScript

$(document).ready(function() {
    // Initialize AOS
    AOS.init({
        duration: 1000,
        easing: 'ease-in-out',
        once: true,
        mirror: false
    });

    // Navbar scroll effect
    $(window).scroll(function() {
        if ($(window).scrollTop() > 100) {
            $('.header').addClass('scrolled');
        } else {
            $('.header').removeClass('scrolled');
        }
    });

    // Smooth scrolling for navigation links
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if (target.length) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 80
            }, 1000);
        }
    });

    // Back to top button
    $(window).scroll(function() {
        if ($(window).scrollTop() > 300) {
            $('#backToTop').addClass('show');
        } else {
            $('#backToTop').removeClass('show');
        }
    });

    $('#backToTop').click(function() {
        $('html, body').animate({scrollTop: 0}, 800);
        return false;
    });

    // Floating call button effects
    $('#floatingCallBtn').on('click', function() {
        // Add click effect
        $(this).addClass('clicked');
        setTimeout(() => {
            $(this).removeClass('clicked');
        }, 200);

        // Track call button click (for analytics)
        console.log('Call button clicked: 0912199991');
    });

    // Contact form submission
    $('#contactForm').on('submit', function(e) {
        e.preventDefault();

        // Get form data
        var formData = {
            name: $(this).find('input[placeholder="Họ và tên *"]').val(),
            phone: $(this).find('input[placeholder="Số điện thoại *"]').val(),
            email: $(this).find('input[placeholder="Email"]').val(),
            products: [],
            note: $(this).find('textarea').val()
        };

        // Get selected products
        $(this).find('input[type="checkbox"]:checked').each(function() {
            formData.products.push($(this).next('label').text());
        });

        // Validate required fields
        if (!formData.name || !formData.phone) {
            console.log('Vui lòng điền đầy đủ thông tin bắt buộc!');
            return;
        }

        // Show success message
        console.log('Cảm ơn bạn đã đăng ký! Chúng tôi sẽ liên hệ với bạn trong thời gian sớm nhất.');

        // Reset form
        this.reset();

        // Log form data (in real application, send to server)
        console.log('Form submitted:', formData);
    });

    // Gallery carousel thumbnail navigation
    $('.thumbnail-item').click(function() {
        var slideIndex = $(this).data('bs-slide-to');
        var carousel = $('#mainGalleryCarousel');

        // Update active thumbnail
        $('.thumbnail-item').removeClass('active');
        $(this).addClass('active');

        // Slide to target
        carousel.carousel(slideIndex);
    });

    // Update thumbnail active state when carousel changes
    $('#mainGalleryCarousel').on('slide.bs.carousel', function(e) {
        var activeIndex = $(e.relatedTarget).index();
        $('.thumbnail-item').removeClass('active');
        $('.thumbnail-item').eq(activeIndex).addClass('active');
    });

    // Amenities carousel thumbnail navigation
    $('.amenities-thumbnail-item').click(function() {
        var slideIndex = $(this).data('bs-slide-to');
        var carousel = $('#amenitiesCarousel');

        // Update active thumbnail
        $('.amenities-thumbnail-item').removeClass('active');
        $(this).addClass('active');

        // Slide to target
        carousel.carousel(slideIndex);
    });

    // Update amenities thumbnail active state when carousel changes
    $('#amenitiesCarousel').on('slide.bs.carousel', function(e) {
        var activeIndex = $(e.relatedTarget).index();
        $('.amenities-thumbnail-item').removeClass('active');
        $('.amenities-thumbnail-item').eq(activeIndex).addClass('active');
    });

    // Gallery lightbox effect
    $('.gallery-item img, .gallery-main-img').click(function() {
        var src = $(this).attr('src');
        var alt = $(this).attr('alt');

        // Create lightbox
        var lightbox = $('<div class="lightbox"></div>');
        var img = $('<img src="' + src + '" alt="' + alt + '">');
        var close = $('<span class="lightbox-close">&times;</span>');

        lightbox.append(img).append(close);
        $('body').append(lightbox);

        // Show lightbox
        lightbox.fadeIn(300);

        // Close lightbox
        close.click(function() {
            lightbox.fadeOut(300, function() {
                lightbox.remove();
            });
        });

        lightbox.click(function(e) {
            if (e.target === this) {
                lightbox.fadeOut(300, function() {
                    lightbox.remove();
                });
            }
        });
    });

    // Mobile menu toggle
    $('.navbar-toggler').click(function() {
        $(this).toggleClass('active');
    });

    // Close mobile menu when clicking on a link
    $('.navbar-nav .nav-link').click(function() {
        $('.navbar-collapse').collapse('hide');
        $('.navbar-toggler').removeClass('active');
    });

    // Parallax effect for hero section
    $(window).scroll(function() {
        var scrolled = $(window).scrollTop();
        var parallax = $('.hero-section');
        var speed = scrolled * 0.5;

        parallax.css('transform', 'translateY(' + speed + 'px)');
    });

    // Counter animation
    function animateCounters() {
        $('.counter').each(function() {
            var $this = $(this);
            var countTo = $this.attr('data-count');

            $({ countNum: $this.text() }).animate({
                countNum: countTo
            }, {
                duration: 2000,
                easing: 'linear',
                step: function() {
                    $this.text(Math.floor(this.countNum));
                },
                complete: function() {
                    $this.text(this.countNum);
                }
            });
        });
    }

    // Trigger counter animation when in viewport
    $(window).scroll(function() {
        $('.counter').each(function() {
            var elementTop = $(this).offset().top;
            var elementBottom = elementTop + $(this).outerHeight();
            var viewportTop = $(window).scrollTop();
            var viewportBottom = viewportTop + $(window).height();

            if (elementBottom > viewportTop && elementTop < viewportBottom) {
                if (!$(this).hasClass('animated')) {
                    $(this).addClass('animated');
                    animateCounters();
                }
            }
        });
    });

    // Lazy loading for images
    function lazyLoadImages() {
        $('img[data-src]').each(function() {
            var $img = $(this);
            var src = $img.attr('data-src');

            if (isInViewport($img)) {
                $img.attr('src', src).removeAttr('data-src');
                $img.on('load', function() {
                    $img.addClass('loaded');
                });
            }
        });
    }

    function isInViewport($element) {
        var elementTop = $element.offset().top;
        var elementBottom = elementTop + $element.outerHeight();
        var viewportTop = $(window).scrollTop();
        var viewportBottom = viewportTop + $(window).height();

        return elementBottom > viewportTop && elementTop < viewportBottom;
    }

    $(window).scroll(lazyLoadImages);
    lazyLoadImages(); // Initial check

    // Preloader with faster removal
    $(window).on('load', function() {
        $('.loading').addClass('hide');
        setTimeout(function() {
            $('.loading').remove();
        }, 300);
    });

    // Add loading animation
    $('body').prepend('<div class="loading"><div class="spinner"></div></div>');

    // Remove loading screen after 3 seconds max
    setTimeout(function() {
        if ($('.loading').length) {
            $('.loading').addClass('hide');
            setTimeout(function() {
                $('.loading').remove();
            }, 300);
        }
    }, 3000);

    // Form validation
    function validateForm(form) {
        var isValid = true;

        form.find('input[required], textarea[required]').each(function() {
            if (!$(this).val().trim()) {
                $(this).addClass('is-invalid');
                isValid = false;
            } else {
                $(this).removeClass('is-invalid');
            }
        });

        return isValid;
    }

    // Real-time form validation
    $('input, textarea').on('blur', function() {
        if ($(this).attr('required') && !$(this).val().trim()) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });

    // Phone number formatting
    $('input[type="tel"]').on('input', function() {
        var value = $(this).val().replace(/\D/g, '');
        $(this).val(value);
    });

    // Email validation
    $('input[type="email"]').on('blur', function() {
        var email = $(this).val();
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

        if (email && !emailRegex.test(email)) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });

    // Sticky elements
    function handleStickyElements() {
        var scrollTop = $(window).scrollTop();

        $('.sticky-element').each(function() {
            var $element = $(this);
            var offsetTop = $element.data('offset-top') || 0;

            if (scrollTop > offsetTop) {
                $element.addClass('is-sticky');
            } else {
                $element.removeClass('is-sticky');
            }
        });
    }

    $(window).scroll(handleStickyElements);

    // Intersection Observer for animations
    if ('IntersectionObserver' in window) {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, {
            threshold: 0.1
        });

        document.querySelectorAll('.fade-in').forEach(el => {
            observer.observe(el);
        });
    }

    // Responsive table
    function makeTablesResponsive() {
        $('table').each(function() {
            if (!$(this).parent().hasClass('table-responsive')) {
                $(this).wrap('<div class="table-responsive"></div>');
            }
        });
    }

    makeTablesResponsive();

    // Print functionality
    window.printPage = function() {
        window.print();
    };

    // Share functionality
    window.shareContent = function(platform, url, title) {
        var shareUrl = '';

        switch(platform) {
            case 'facebook':
                shareUrl = 'https://www.facebook.com/sharer/sharer.php?u=' + encodeURIComponent(url);
                break;
            case 'twitter':
                shareUrl = 'https://twitter.com/intent/tweet?url=' + encodeURIComponent(url) + '&text=' + encodeURIComponent(title);
                break;
            case 'linkedin':
                shareUrl = 'https://www.linkedin.com/sharing/share-offsite/?url=' + encodeURIComponent(url);
                break;
        }

        if (shareUrl) {
            window.open(shareUrl, '_blank', 'width=600,height=400');
        }
    };

    // Copy to clipboard
    window.copyToClipboard = function(text) {
        navigator.clipboard.writeText(text).then(function() {
            console.log('Đã sao chép vào clipboard!');
        });
    };

    // Scroll to section
    window.scrollToSection = function(sectionId) {
        var target = $('#' + sectionId);
        if (target.length) {
            $('html, body').animate({
                scrollTop: target.offset().top - 80
            }, 1000);
        }
    };

    // Initialize tooltips
    $('[data-bs-toggle="tooltip"]').tooltip();

    // Initialize popovers
    $('[data-bs-toggle="popover"]').popover();

    // Video auto play/pause with Intersection Observer
    function initVideoAutoPlay() {
        var videoElement = document.querySelector('#video iframe');
        if (!videoElement) return;

        // Create intersection observer
        var observer = new IntersectionObserver(function(entries) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    // Video is visible, play it
                    setTimeout(function() {
                        videoElement.contentWindow.postMessage('{"event":"command","func":"playVideo","args":""}', '*');
                    }, 500);
                } else {
                    // Video is not visible, pause it
                    videoElement.contentWindow.postMessage('{"event":"command","func":"pauseVideo","args":""}', '*');
                }
            });
        }, {
            threshold: 0.5, // Trigger when 50% of video is visible
            rootMargin: '0px 0px -100px 0px' // Add some margin
        });

        // Start observing the video section
        var videoSection = document.querySelector('#video');
        if (videoSection) {
            observer.observe(videoSection);
        }
    }

    // Initialize video auto play when page loads
    setTimeout(initVideoAutoPlay, 1000);

    // Auto-hide alerts
    $('.alert').each(function() {
        var $alert = $(this);
        if ($alert.data('auto-hide')) {
            setTimeout(function() {
                $alert.fadeOut();
            }, $alert.data('auto-hide'));
        }
    });

    // Keyboard navigation
    $(document).keydown(function(e) {
        // ESC key to close modals/lightboxes
        if (e.keyCode === 27) {
            $('.lightbox').fadeOut(300, function() {
                $(this).remove();
            });
        }
    });

    // Performance optimization
    let ticking = false;

    function updateOnScroll() {
        // Batch scroll-related updates
        handleStickyElements();
        ticking = false;
    }

    $(window).scroll(function() {
        if (!ticking) {
            requestAnimationFrame(updateOnScroll);
            ticking = true;
        }
    });
});

// YouTube API for hero video
var player;
function onYouTubeIframeAPIReady() {
    player = new YT.Player('hero-video-container', {
        height: '100%',
        width: '100%',
        videoId: 'TK_X4J3vKYs', // Video ID from the YouTube URL
        playerVars: {
            'autoplay': 1,
            'controls': 0,
            'showinfo': 0,
            'modestbranding': 1,
            'loop': 1,
            'playlist': 'TK_X4J3vKYs',
            'fs': 0,
            'cc_load_policy': 0,
            'iv_load_policy': 3,
            'autohide': 0,
            'mute': 1
        },
        events: {
            'onReady': onPlayerReady,
            'onStateChange': onPlayerStateChange
        }
    });
}

function onPlayerReady(event) {
    event.target.mute();
    event.target.playVideo();
}

function onPlayerStateChange(event) {
    if (event.data == YT.PlayerState.ENDED) {
        player.playVideo();
    }
}

// Lightbox CSS (injected via JavaScript)
$(document).ready(function() {
    var lightboxCSS = `
        <style>
        .lightbox {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        }

        .lightbox img {
            max-width: 90%;
            max-height: 90%;
            object-fit: contain;
        }

        .lightbox-close {
            position: absolute;
            top: 20px;
            right: 30px;
            color: white;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
            z-index: 10001;
        }

        .lightbox-close:hover {
            opacity: 0.7;
        }
        </style>
    `;

    $('head').append(lightboxCSS);
});


<?php

use <PERSON><PERSON><PERSON>\Theme\Events\RenderingThemeOptionSettings;
use Bo<PERSON>ble\Theme\ThemeOption\Fields\MediaImageField;
use <PERSON><PERSON>ble\Theme\ThemeOption\Fields\RepeaterField;
use Bo<PERSON>ble\Theme\ThemeOption\Fields\TextareaField;
use Bo<PERSON>ble\Theme\ThemeOption\Fields\TextField;

app('events')->listen(RenderingThemeOptionSettings::class, function (): void {
    // Cài đặt chung
    theme_option()
        ->setSection([
            'id' => 'opt-text-subsection-general',
            'title' => __('Cài đặt chung'),
            'icon' => 'ti ti-settings',
        ])
        ->setField([
            'id' => 'primary_color',
            'section_id' => 'opt-text-subsection-general',
            'type' => 'customColor',
            'label' => __('Màu chính'),
            'attributes' => [
                'name' => 'primary_color',
                'value' => '#28a745',
            ],
        ])
        ->setField([
            'id' => 'site_title',
            'section_id' => 'opt-text-subsection-general',
            'type' => 'text',
            'label' => __('Tiêu đề website'),
            'attributes' => [
                'name' => 'site_title',
                'value' => 'Green City Bình Dương',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'contact_phone',
            'section_id' => 'opt-text-subsection-general',
            'type' => 'text',
            'label' => __('Số điện thoại liên hệ'),
            'attributes' => [
                'name' => 'contact_phone',
                'value' => '0708808891',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ]);

    // Banner/Hero Section
    theme_option()
        ->setSection([
            'id' => 'opt-text-subsection-banner',
            'title' => __('Banner/Video Hero'),
            'icon' => 'ti ti-photo',
        ])
        ->setField([
            'id' => 'hero_video_url',
            'section_id' => 'opt-text-subsection-banner',
            'type' => 'text',
            'label' => __('URL Video YouTube'),
            'attributes' => [
                'name' => 'hero_video_url',
                'value' => 'https://www.youtube.com/embed/Y_Mewkp3RvY?autoplay=1&mute=1&loop=1&playlist=Y_Mewkp3RvY&controls=0&showinfo=0&modestbranding=1&fs=0&cc_load_policy=0&iv_load_policy=3&autohide=0&rel=0',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ]);

    // Navigation Section
    theme_option()
        ->setSection([
            'id' => 'opt-text-subsection-navigation',
            'title' => __('Menu điều hướng'),
            'icon' => 'ti ti-menu',
        ])
        ->setField([
            'id' => 'nav_tong_quan',
            'section_id' => 'opt-text-subsection-navigation',
            'type' => 'text',
            'label' => __('Tổng Quan'),
            'attributes' => [
                'name' => 'nav_tong_quan',
                'value' => 'Tổng Quan',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'nav_vi_tri',
            'section_id' => 'opt-text-subsection-navigation',
            'type' => 'text',
            'label' => __('Vị Trí'),
            'attributes' => [
                'name' => 'nav_vi_tri',
                'value' => 'Vị Trí',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'nav_tien_ich',
            'section_id' => 'opt-text-subsection-navigation',
            'type' => 'text',
            'label' => __('Tiện Ích'),
            'attributes' => [
                'name' => 'nav_tien_ich',
                'value' => 'Tiện Ích',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'nav_mat_bang',
            'section_id' => 'opt-text-subsection-navigation',
            'type' => 'text',
            'label' => __('Mặt Bằng'),
            'attributes' => [
                'name' => 'nav_mat_bang',
                'value' => 'Mặt Bằng',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'nav_thanh_toan',
            'section_id' => 'opt-text-subsection-navigation',
            'type' => 'text',
            'label' => __('Thanh Toán'),
            'attributes' => [
                'name' => 'nav_thanh_toan',
                'value' => 'Thanh Toán',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'nav_hinh_anh',
            'section_id' => 'opt-text-subsection-navigation',
            'type' => 'text',
            'label' => __('Hình Ảnh'),
            'attributes' => [
                'name' => 'nav_hinh_anh',
                'value' => 'Hình Ảnh',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'nav_video',
            'section_id' => 'opt-text-subsection-navigation',
            'type' => 'text',
            'label' => __('Video'),
            'attributes' => [
                'name' => 'nav_video',
                'value' => 'Video',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'nav_lien_he',
            'section_id' => 'opt-text-subsection-navigation',
            'type' => 'text',
            'label' => __('Liên Hệ'),
            'attributes' => [
                'name' => 'nav_lien_he',
                'value' => 'Liên Hệ',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ]);

    // Tổng Quan Section
    theme_option()
        ->setSection([
            'id' => 'opt-text-subsection-tong-quan',
            'title' => __('Tổng Quan Dự Án'),
            'icon' => 'ti ti-info-circle',
        ])
        ->setField([
            'id' => 'tong_quan_title',
            'section_id' => 'opt-text-subsection-tong-quan',
            'type' => 'text',
            'label' => __('Tiêu đề'),
            'attributes' => [
                'name' => 'tong_quan_title',
                'value' => 'TỔNG QUAN',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'project_name',
            'section_id' => 'opt-text-subsection-tong-quan',
            'type' => 'text',
            'label' => __('Tên dự án'),
            'attributes' => [
                'name' => 'project_name',
                'value' => 'Green City Bình Dương',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'project_developer',
            'section_id' => 'opt-text-subsection-tong-quan',
            'type' => 'text',
            'label' => __('Chủ đầu tư'),
            'attributes' => [
                'name' => 'project_developer',
                'value' => 'Becamex IDC',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'project_location',
            'section_id' => 'opt-text-subsection-tong-quan',
            'type' => 'text',
            'label' => __('Vị trí'),
            'attributes' => [
                'name' => 'project_location',
                'value' => 'Phường Bình Dương, Tp Hồ Chí Minh',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'project_area_units',
            'section_id' => 'opt-text-subsection-tong-quan',
            'type' => 'text',
            'label' => __('Diện tích & Số lượng sản phẩm'),
            'attributes' => [
                'name' => 'project_area_units',
                'value' => '20 ha gần 1400 căn',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'project_product_types',
            'section_id' => 'opt-text-subsection-tong-quan',
            'type' => 'text',
            'label' => __('Loại hình sản phẩm'),
            'attributes' => [
                'name' => 'project_product_types',
                'value' => 'Shophouse, Nhà Phố Liền Kề',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'project_building_standards',
            'section_id' => 'opt-text-subsection-tong-quan',
            'type' => 'text',
            'label' => __('Quy chuẩn xây dựng'),
            'attributes' => [
                'name' => 'project_building_standards',
                'value' => '1 Trệt – 1 Lửng – 2 Lầu (110m² – 150m²)',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'project_handover_time',
            'section_id' => 'opt-text-subsection-tong-quan',
            'type' => 'text',
            'label' => __('Thời gian bàn giao'),
            'attributes' => [
                'name' => 'project_handover_time',
                'value' => 'Dự kiến năm 2026',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'project_bank_support',
            'section_id' => 'opt-text-subsection-tong-quan',
            'type' => 'text',
            'label' => __('Ngân hàng hỗ trợ'),
            'attributes' => [
                'name' => 'project_bank_support',
                'value' => 'BIDV',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ]);

    // Vị Trí Section
    theme_option()
        ->setSection([
            'id' => 'opt-text-subsection-vi-tri',
            'title' => __('Vị Trí'),
            'icon' => 'ti ti-map-pin',
        ])
        ->setField([
            'id' => 'vi_tri_title',
            'section_id' => 'opt-text-subsection-vi-tri',
            'type' => 'text',
            'label' => __('Tiêu đề'),
            'attributes' => [
                'name' => 'vi_tri_title',
                'value' => 'VỊ TRÍ GREEN CITY BÌNH DƯƠNG',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'vi_tri_subtitle',
            'section_id' => 'opt-text-subsection-vi-tri',
            'type' => 'text',
            'label' => __('Phụ đề'),
            'attributes' => [
                'name' => 'vi_tri_subtitle',
                'value' => 'THÀNH PHỐ MỚI BÌNH DƯƠNG',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'vi_tri_content_title_1',
            'section_id' => 'opt-text-subsection-vi-tri',
            'type' => 'text',
            'label' => __('Tiêu đề nội dung 1'),
            'attributes' => [
                'name' => 'vi_tri_content_title_1',
                'value' => 'Green City Bình Dương – Đòn bẩy tăng trưởng giữa trung tâm kinh tế trọng điểm',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'vi_tri_content_desc_1',
            'section_id' => 'opt-text-subsection-vi-tri',
            'type' => 'textarea',
            'label' => __('Mô tả nội dung 1'),
            'attributes' => [
                'name' => 'vi_tri_content_desc_1',
                'value' => 'Green City Bình Dương tọa lạc ngay trung tâm Thành phố mới Bình Dương ngay góc đường huyết mạch Đại lộ Lê Lợi, Võ Nguyên Giáp. Dự án nằm trong vùng quy hoạch chiến lược, kết nối trực tiếp đến Trung tâm Hành Chính, KCN Mỹ Phước, KCN Vsip II, Quốc lộ 13, mở ra cơ hội sinh lời vượt trội.',
                'options' => [
                    'class' => 'form-control',
                    'rows' => 3,
                ],
            ],
        ])
        ->setField([
            'id' => 'vi_tri_content_title_2',
            'section_id' => 'opt-text-subsection-vi-tri',
            'type' => 'text',
            'label' => __('Tiêu đề nội dung 2'),
            'attributes' => [
                'name' => 'vi_tri_content_title_2',
                'value' => 'Green City Bình Dương – Nơi khởi đầu cuộc sống xanh giữa lòng đô thị mới',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'vi_tri_content_desc_2',
            'section_id' => 'opt-text-subsection-vi-tri',
            'type' => 'textarea',
            'label' => __('Mô tả nội dung 2'),
            'attributes' => [
                'name' => 'vi_tri_content_desc_2',
                'value' => 'Dự án sở hữu môi trường sống trong lành, quy hoạch hiện đại, tiện ích đồng bộ – là nơi lý tưởng để xây dựng tổ ấm, an cư lâu dài giữa vùng đô thị phát triển năng động bậc nhất miền Nam.',
                'options' => [
                    'class' => 'form-control',
                    'rows' => 3,
                ],
            ],
        ])
        ->setField([
            'id' => 'vi_tri_image',
            'section_id' => 'opt-text-subsection-vi-tri',
            'type' => 'mediaImage',
            'label' => __('Hình ảnh vị trí'),
            'attributes' => [
                'name' => 'vi_tri_image',
                'value' => '',
            ],
        ]);

    // Liên Kết Vùng Section
    theme_option()
        ->setSection([
            'id' => 'opt-text-subsection-lien-ket-vung',
            'title' => __('Liên Kết Vùng'),
            'icon' => 'ti ti-map',
        ])
        ->setField([
            'id' => 'lien_ket_vung_title',
            'section_id' => 'opt-text-subsection-lien-ket-vung',
            'type' => 'text',
            'label' => __('Tiêu đề'),
            'attributes' => [
                'name' => 'lien_ket_vung_title',
                'value' => 'LIÊN KẾT VÙNG GREEN CITY BÌNH DƯƠNG',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'lien_ket_vung_subtitle',
            'section_id' => 'opt-text-subsection-lien-ket-vung',
            'type' => 'text',
            'label' => __('Phụ đề'),
            'attributes' => [
                'name' => 'lien_ket_vung_subtitle',
                'value' => 'Liên kết vùng & Tiềm năng phát triển của Green City Bình Dương',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'lien_ket_vung_content_title',
            'section_id' => 'opt-text-subsection-lien-ket-vung',
            'type' => 'text',
            'label' => __('Tiêu đề nội dung'),
            'attributes' => [
                'name' => 'lien_ket_vung_content_title',
                'value' => 'Liên kết Vùng chiến lược của Green City Bình Dương',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'lien_ket_vung_content_desc',
            'section_id' => 'opt-text-subsection-lien-ket-vung',
            'type' => 'textarea',
            'label' => __('Mô tả nội dung'),
            'attributes' => [
                'name' => 'lien_ket_vung_content_desc',
                'value' => 'Green City Bình Dương nằm trong lòng Trung tâm Thành phố mới Bình Dương – khu vực đầu tàu quy hoạch theo mô hình đô thị thông minh, hiện đại và đa chức năng. Đây chính là trung tâm hành chính, tài chính, giáo dục và công nghệ cao của tỉnh Bình Dương.',
                'options' => [
                    'class' => 'form-control',
                    'rows' => 3,
                ],
            ],
        ])
        ->setField([
            'id' => 'lien_ket_vung_map_image',
            'section_id' => 'opt-text-subsection-lien-ket-vung',
            'type' => 'mediaImage',
            'label' => __('Hình ảnh bản đồ liên kết vùng'),
            'attributes' => [
                'name' => 'lien_ket_vung_map_image',
                'value' => '',
            ],
        ]);

    // Tiện Ích Section
    theme_option()
        ->setSection([
            'id' => 'opt-text-subsection-tien-ich',
            'title' => __('Tiện Ích'),
            'icon' => 'ti ti-building-community',
        ])
        ->setField([
            'id' => 'tien_ich_title',
            'section_id' => 'opt-text-subsection-tien-ich',
            'type' => 'text',
            'label' => __('Tiêu đề'),
            'attributes' => [
                'name' => 'tien_ich_title',
                'value' => 'TIỆN ÍCH GREEN CITY BÌNH DƯƠNG',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'tien_ich_noi_khu_title',
            'section_id' => 'opt-text-subsection-tien-ich',
            'type' => 'text',
            'label' => __('Tiêu đề tiện ích nội khu'),
            'attributes' => [
                'name' => 'tien_ich_noi_khu_title',
                'value' => 'Tiện ích nội khu:',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_title',
            'section_id' => 'opt-text-subsection-tien-ich',
            'type' => 'text',
            'label' => __('Tiêu đề tiện ích ngoại khu'),
            'attributes' => [
                'name' => 'tien_ich_ngoai_khu_title',
                'value' => 'Tiện ích ngoại khu vượt trội:',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_desc',
            'section_id' => 'opt-text-subsection-tien-ich',
            'type' => 'textarea',
            'label' => __('Mô tả tiện ích ngoại khu'),
            'attributes' => [
                'name' => 'tien_ich_ngoai_khu_desc',
                'value' => 'Nằm ngay vị trí lõi thành phố mới Bình Dương, Green City thừa hưởng trọn vẹn hệ thống tiện ích hiện đại của khu vực:',
                'options' => [
                    'class' => 'form-control',
                    'rows' => 2,
                ],
            ],
        ]);

    // Tiện Ích Nội Khu Section
    theme_option()
        ->setSection([
            'id' => 'opt-text-subsection-tien-ich-noi-khu',
            'title' => __('Tiện Ích Nội Khu'),
            'icon' => 'ti ti-home',
        ])
        ->setField([
            'id' => 'tien_ich_noi_khu_1_image',
            'section_id' => 'opt-text-subsection-tien-ich-noi-khu',
            'type' => 'mediaImage',
            'label' => __('Green School - Hình ảnh'),
            'attributes' => [
                'name' => 'tien_ich_noi_khu_1_image',
                'value' => '',
            ],
        ])
        ->setField([
            'id' => 'tien_ich_noi_khu_1_title',
            'section_id' => 'opt-text-subsection-tien-ich-noi-khu',
            'type' => 'text',
            'label' => __('Green School - Tiêu đề'),
            'attributes' => [
                'name' => 'tien_ich_noi_khu_1_title',
                'value' => 'Green School',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'tien_ich_noi_khu_1_desc',
            'section_id' => 'opt-text-subsection-tien-ich-noi-khu',
            'type' => 'text',
            'label' => __('Green School - Mô tả'),
            'attributes' => [
                'name' => 'tien_ich_noi_khu_1_desc',
                'value' => 'Trường mầm non với thương hiệu Green School',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'tien_ich_noi_khu_2_image',
            'section_id' => 'opt-text-subsection-tien-ich-noi-khu',
            'type' => 'mediaImage',
            'label' => __('Green Mall - Hình ảnh'),
            'attributes' => [
                'name' => 'tien_ich_noi_khu_2_image',
                'value' => '',
            ],
        ])
        ->setField([
            'id' => 'tien_ich_noi_khu_2_title',
            'section_id' => 'opt-text-subsection-tien-ich-noi-khu',
            'type' => 'text',
            'label' => __('Green Mall - Tiêu đề'),
            'attributes' => [
                'name' => 'tien_ich_noi_khu_2_title',
                'value' => 'Green Mall',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'tien_ich_noi_khu_2_desc',
            'section_id' => 'opt-text-subsection-tien-ich-noi-khu',
            'type' => 'text',
            'label' => __('Green Mall - Mô tả'),
            'attributes' => [
                'name' => 'tien_ich_noi_khu_2_desc',
                'value' => 'Trung tâm thương mại đẳng cấp nội khu',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'tien_ich_noi_khu_3_image',
            'section_id' => 'opt-text-subsection-tien-ich-noi-khu',
            'type' => 'mediaImage',
            'label' => __('Green Garden - Hình ảnh'),
            'attributes' => [
                'name' => 'tien_ich_noi_khu_3_image',
                'value' => '',
            ],
        ])
        ->setField([
            'id' => 'tien_ich_noi_khu_3_title',
            'section_id' => 'opt-text-subsection-tien-ich-noi-khu',
            'type' => 'text',
            'label' => __('Green Garden - Tiêu đề'),
            'attributes' => [
                'name' => 'tien_ich_noi_khu_3_title',
                'value' => 'Green Garden',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'tien_ich_noi_khu_3_desc',
            'section_id' => 'opt-text-subsection-tien-ich-noi-khu',
            'type' => 'text',
            'label' => __('Green Garden - Mô tả'),
            'attributes' => [
                'name' => 'tien_ich_noi_khu_3_desc',
                'value' => 'Công viên và vườn đi bộ cao cấp',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'tien_ich_noi_khu_4_image',
            'section_id' => 'opt-text-subsection-tien-ich-noi-khu',
            'type' => 'mediaImage',
            'label' => __('Sinh hoạt cộng đồng - Hình ảnh'),
            'attributes' => [
                'name' => 'tien_ich_noi_khu_4_image',
                'value' => '',
            ],
        ])
        ->setField([
            'id' => 'tien_ich_noi_khu_4_title',
            'section_id' => 'opt-text-subsection-tien-ich-noi-khu',
            'type' => 'text',
            'label' => __('Sinh hoạt cộng đồng - Tiêu đề'),
            'attributes' => [
                'name' => 'tien_ich_noi_khu_4_title',
                'value' => 'Không gian sinh hoạt chung',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'tien_ich_noi_khu_4_desc',
            'section_id' => 'opt-text-subsection-tien-ich-noi-khu',
            'type' => 'text',
            'label' => __('Sinh hoạt cộng đồng - Mô tả'),
            'attributes' => [
                'name' => 'tien_ich_noi_khu_4_desc',
                'value' => 'Khu vực sinh hoạt cộng đồng hiện đại',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'tien_ich_noi_khu_5_image',
            'section_id' => 'opt-text-subsection-tien-ich-noi-khu',
            'type' => 'mediaImage',
            'label' => __('Green Gym - Hình ảnh'),
            'attributes' => [
                'name' => 'tien_ich_noi_khu_5_image',
                'value' => '',
            ],
        ])
        ->setField([
            'id' => 'tien_ich_noi_khu_5_title',
            'section_id' => 'opt-text-subsection-tien-ich-noi-khu',
            'type' => 'text',
            'label' => __('Green Gym - Tiêu đề'),
            'attributes' => [
                'name' => 'tien_ich_noi_khu_5_title',
                'value' => 'Green Gym',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'tien_ich_noi_khu_5_desc',
            'section_id' => 'opt-text-subsection-tien-ich-noi-khu',
            'type' => 'text',
            'label' => __('Green Gym - Mô tả'),
            'attributes' => [
                'name' => 'tien_ich_noi_khu_5_desc',
                'value' => 'Phòng tập Gym cao cấp thương hiệu Green Gym',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ]);

    // Tiện Ích Ngoại Khu Section
    theme_option()
        ->setSection([
            'id' => 'opt-text-subsection-tien-ich-ngoai-khu',
            'title' => __('Tiện Ích Ngoại Khu'),
            'icon' => 'ti ti-building-estate',
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_1_image',
            'section_id' => 'opt-text-subsection-tien-ich-ngoai-khu',
            'type' => 'mediaImage',
            'label' => __('TTHC Bình Dương - Hình ảnh'),
            'attributes' => [
                'name' => 'tien_ich_ngoai_khu_1_image',
                'value' => '',
            ],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_1_title',
            'section_id' => 'opt-text-subsection-tien-ich-ngoai-khu',
            'type' => 'text',
            'label' => __('TTHC Bình Dương - Tiêu đề'),
            'attributes' => [
                'name' => 'tien_ich_ngoai_khu_1_title',
                'value' => 'TTHC Bình Dương',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_1_desc',
            'section_id' => 'opt-text-subsection-tien-ich-ngoai-khu',
            'type' => 'text',
            'label' => __('TTHC Bình Dương - Mô tả'),
            'attributes' => [
                'name' => 'tien_ich_ngoai_khu_1_desc',
                'value' => 'Trung tâm Hành chính tỉnh Bình Dương',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_2_image',
            'section_id' => 'opt-text-subsection-tien-ich-ngoai-khu',
            'type' => 'mediaImage',
            'label' => __('KCN VSIP II - Hình ảnh'),
            'attributes' => [
                'name' => 'tien_ich_ngoai_khu_2_image',
                'value' => '',
            ],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_2_title',
            'section_id' => 'opt-text-subsection-tien-ich-ngoai-khu',
            'type' => 'text',
            'label' => __('KCN VSIP II - Tiêu đề'),
            'attributes' => [
                'name' => 'tien_ich_ngoai_khu_2_title',
                'value' => 'KCN VSIP II',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_2_desc',
            'section_id' => 'opt-text-subsection-tien-ich-ngoai-khu',
            'type' => 'text',
            'label' => __('KCN VSIP II - Mô tả'),
            'attributes' => [
                'name' => 'tien_ich_ngoai_khu_2_desc',
                'value' => 'Khu Công nghiệp VSIP II và các tập đoàn đa quốc gia',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_3_image',
            'section_id' => 'opt-text-subsection-tien-ich-ngoai-khu',
            'type' => 'mediaImage',
            'label' => __('Đại học Quốc tế Miền Đông - Hình ảnh'),
            'attributes' => [
                'name' => 'tien_ich_ngoai_khu_3_image',
                'value' => '',
            ],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_3_title',
            'section_id' => 'opt-text-subsection-tien-ich-ngoai-khu',
            'type' => 'text',
            'label' => __('Đại học Quốc tế Miền Đông - Tiêu đề'),
            'attributes' => [
                'name' => 'tien_ich_ngoai_khu_3_title',
                'value' => 'Đại học Quốc tế Miền Đông',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_3_desc',
            'section_id' => 'opt-text-subsection-tien-ich-ngoai-khu',
            'type' => 'text',
            'label' => __('Đại học Quốc tế Miền Đông - Mô tả'),
            'attributes' => [
                'name' => 'tien_ich_ngoai_khu_3_desc',
                'value' => 'Mạng lưới trường học liên cấp chuẩn quốc tế',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_4_image',
            'section_id' => 'opt-text-subsection-tien-ich-ngoai-khu',
            'type' => 'mediaImage',
            'label' => __('Bệnh viện 1500 giường - Hình ảnh'),
            'attributes' => [
                'name' => 'tien_ich_ngoai_khu_4_image',
                'value' => '',
            ],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_4_title',
            'section_id' => 'opt-text-subsection-tien-ich-ngoai-khu',
            'type' => 'text',
            'label' => __('Bệnh viện 1500 giường - Tiêu đề'),
            'attributes' => [
                'name' => 'tien_ich_ngoai_khu_4_title',
                'value' => 'Bệnh viện 1500 giường',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_4_desc',
            'section_id' => 'opt-text-subsection-tien-ich-ngoai-khu',
            'type' => 'text',
            'label' => __('Bệnh viện 1500 giường - Mô tả'),
            'attributes' => [
                'name' => 'tien_ich_ngoai_khu_4_desc',
                'value' => 'Hệ thống y tế cao cấp và hiện đại',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_5_image',
            'section_id' => 'opt-text-subsection-tien-ich-ngoai-khu',
            'type' => 'mediaImage',
            'label' => __('AEON Mall - Hình ảnh'),
            'attributes' => [
                'name' => 'tien_ich_ngoai_khu_5_image',
                'value' => '',
            ],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_5_title',
            'section_id' => 'opt-text-subsection-tien-ich-ngoai-khu',
            'type' => 'text',
            'label' => __('AEON Mall - Tiêu đề'),
            'attributes' => [
                'name' => 'tien_ich_ngoai_khu_5_title',
                'value' => 'AEON Mall',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_5_desc',
            'section_id' => 'opt-text-subsection-tien-ich-ngoai-khu',
            'type' => 'text',
            'label' => __('AEON Mall - Mô tả'),
            'attributes' => [
                'name' => 'tien_ich_ngoai_khu_5_desc',
                'value' => 'Trung tâm thương mại và siêu thị hiện đại',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_6_image',
            'section_id' => 'opt-text-subsection-tien-ich-ngoai-khu',
            'type' => 'mediaImage',
            'label' => __('Quốc lộ 13 - Hình ảnh'),
            'attributes' => [
                'name' => 'tien_ich_ngoai_khu_6_image',
                'value' => '',
            ],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_6_title',
            'section_id' => 'opt-text-subsection-tien-ich-ngoai-khu',
            'type' => 'text',
            'label' => __('Quốc lộ 13 - Tiêu đề'),
            'attributes' => [
                'name' => 'tien_ich_ngoai_khu_6_title',
                'value' => 'Quốc lộ 13',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_6_desc',
            'section_id' => 'opt-text-subsection-tien-ich-ngoai-khu',
            'type' => 'text',
            'label' => __('Quốc lộ 13 - Mô tả'),
            'attributes' => [
                'name' => 'tien_ich_ngoai_khu_6_desc',
                'value' => 'Kết nối các trục giao thông huyết mạch',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ]);

    // Chủ Đầu Tư Section
    theme_option()
        ->setSection([
            'id' => 'opt-text-subsection-chu-dau-tu',
            'title' => __('Chủ Đầu Tư'),
            'icon' => 'ti ti-building-skyscraper',
        ])
        ->setField([
            'id' => 'chu_dau_tu_title',
            'section_id' => 'opt-text-subsection-chu-dau-tu',
            'type' => 'text',
            'label' => __('Tiêu đề'),
            'attributes' => [
                'name' => 'chu_dau_tu_title',
                'value' => 'CHỦ ĐẦU TƯ UY TÍN',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'chu_dau_tu_subtitle',
            'section_id' => 'opt-text-subsection-chu-dau-tu',
            'type' => 'text',
            'label' => __('Phụ đề'),
            'attributes' => [
                'name' => 'chu_dau_tu_subtitle',
                'value' => 'BECAMEX IDC - TẬP ĐOÀN HÀNG ĐẦU VIỆT NAM',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'chu_dau_tu_name',
            'section_id' => 'opt-text-subsection-chu-dau-tu',
            'type' => 'text',
            'label' => __('Tên chủ đầu tư'),
            'attributes' => [
                'name' => 'chu_dau_tu_name',
                'value' => 'Tổng Công ty Đầu tư và Phát triển Công nghiệp (Becamex IDC)',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'chu_dau_tu_description',
            'section_id' => 'opt-text-subsection-chu-dau-tu',
            'type' => 'textarea',
            'label' => __('Mô tả chủ đầu tư'),
            'attributes' => [
                'name' => 'chu_dau_tu_description',
                'value' => 'Đơn vị phát triển hạ tầng và đô thị hàng đầu Việt Nam với hơn 30 năm kinh nghiệm. Becamex IDC đã thành công phát triển nhiều khu đô thị, khu công nghiệp quy mô lớn tại Bình Dương và các tỉnh thành khác.',
                'options' => [
                    'class' => 'form-control',
                    'rows' => 3,
                ],
            ],
        ])
        ->setField([
            'id' => 'chu_dau_tu_logo',
            'section_id' => 'opt-text-subsection-chu-dau-tu',
            'type' => 'mediaImage',
            'label' => __('Logo chủ đầu tư'),
            'attributes' => [
                'name' => 'chu_dau_tu_logo',
                'value' => '',
            ],
        ]);

    // Mặt Bằng Section
    theme_option()
        ->setSection([
            'id' => 'opt-text-subsection-mat-bang',
            'title' => __('Mặt Bằng'),
            'icon' => 'ti ti-layout-grid',
        ])
        ->setField([
            'id' => 'mat_bang_title',
            'section_id' => 'opt-text-subsection-mat-bang',
            'type' => 'text',
            'label' => __('Tiêu đề'),
            'attributes' => [
                'name' => 'mat_bang_title',
                'value' => 'MẶT BẰNG DỰ ÁN',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'mat_bang_image',
            'section_id' => 'opt-text-subsection-mat-bang',
            'type' => 'text',
            'label' => __('URL Hình ảnh mặt bằng'),
            'attributes' => [
                'name' => 'mat_bang_image',
                'value' => 'https://greencitybecamex.vn/wp-content/uploads/2025/03/green-city-binh-duong-mat-bang.jpg',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ]);

    // Thanh Toán Section
    theme_option()
        ->setSection([
            'id' => 'opt-text-subsection-thanh-toan',
            'title' => __('Thanh Toán'),
            'icon' => 'ti ti-credit-card',
        ])
        ->setField([
            'id' => 'thanh_toan_title',
            'section_id' => 'opt-text-subsection-thanh-toan',
            'type' => 'text',
            'label' => __('Tiêu đề'),
            'attributes' => [
                'name' => 'thanh_toan_title',
                'value' => 'PHƯƠNG THỨC THANH TOÁN',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'loan_interest_rate',
            'section_id' => 'opt-text-subsection-thanh-toan',
            'type' => 'text',
            'label' => __('Lãi suất vay'),
            'attributes' => [
                'name' => 'loan_interest_rate',
                'value' => 'dự kiến 6,5%/năm',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'loan_support_ratio',
            'section_id' => 'opt-text-subsection-thanh-toan',
            'type' => 'text',
            'label' => __('Tỷ lệ hỗ trợ'),
            'attributes' => [
                'name' => 'loan_support_ratio',
                'value' => 'tối đa 70% giá trị Hợp đồng mua bán Nhà ở/đất ở ký kết với Chủ đầu tư',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'loan_duration',
            'section_id' => 'opt-text-subsection-thanh-toan',
            'type' => 'text',
            'label' => __('Thời gian vay'),
            'attributes' => [
                'name' => 'loan_duration',
                'value' => 'tối đa 30 năm',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'price_table_title',
            'section_id' => 'opt-text-subsection-thanh-toan',
            'type' => 'text',
            'label' => __('Tiêu đề bảng giá'),
            'attributes' => [
                'name' => 'price_table_title',
                'value' => 'BẢNG GIÁ BÁN GREEN CITY BÌNH DƯƠNG',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'price_note',
            'section_id' => 'opt-text-subsection-thanh-toan',
            'type' => 'textarea',
            'label' => __('Ghi chú bảng giá'),
            'attributes' => [
                'name' => 'price_note',
                'value' => 'Giá bán liền kề, shophouse Green City trung bình khoảng —, bảng giá Green City Bình Dương sẽ được chủ đầu tư Becamex IDC công bố đồng loạt từng lô trong ngày mở bán.',
                'options' => [
                    'class' => 'form-control',
                    'rows' => 2,
                ],
            ],
        ]);

    // Hình Ảnh Section - Slides
    theme_option()
        ->setSection([
            'id' => 'opt-text-subsection-gallery-slides',
            'title' => __('Slides Hình Ảnh'),
            'icon' => 'ti ti-photo-circle',
        ])
        ->setField([
            'id' => 'gallery_title',
            'section_id' => 'opt-text-subsection-gallery-slides',
            'type' => 'text',
            'label' => __('Tiêu đề'),
            'attributes' => [
                'name' => 'gallery_title',
                'value' => 'HÌNH ẢNH DỰ ÁN',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField(
            RepeaterField::make()
                ->id('gallery_slides')
                ->sectionId('opt-text-subsection-gallery-slides')
                ->name('gallery_slides')
                ->label(__('Slides hình ảnh'))
                ->defaultValue([])
                ->fields([
                    MediaImageField::make()
                        ->name('image')
                        ->label(__('Hình ảnh')),
                    TextField::make()
                        ->name('title')
                        ->label(__('Tiêu đề')),
                    TextareaField::make()
                        ->name('description')
                        ->label(__('Mô tả'))
                        ->rows(2),
                ])
                ->toArray()
        );

    // Video Section
    theme_option()
        ->setSection([
            'id' => 'opt-text-subsection-video',
            'title' => __('Video'),
            'icon' => 'ti ti-video',
        ])
        ->setField([
            'id' => 'video_title',
            'section_id' => 'opt-text-subsection-video',
            'type' => 'text',
            'label' => __('Tiêu đề'),
            'attributes' => [
                'name' => 'video_title',
                'value' => 'VIDEO GIỚI THIỆU',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'video_embed_url',
            'section_id' => 'opt-text-subsection-video',
            'type' => 'text',
            'label' => __('URL Video YouTube Embed'),
            'attributes' => [
                'name' => 'video_embed_url',
                'value' => 'https://www.youtube.com/embed/9po0pfbZRNM?enablejsapi=1&origin=https://greencity-binhduong.com',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ]);

    // Liên Hệ Section
    theme_option()
        ->setSection([
            'id' => 'opt-text-subsection-lien-he',
            'title' => __('Liên Hệ'),
            'icon' => 'ti ti-phone',
        ])
        ->setField([
            'id' => 'lien_he_title',
            'section_id' => 'opt-text-subsection-lien-he',
            'type' => 'text',
            'label' => __('Tiêu đề'),
            'attributes' => [
                'name' => 'lien_he_title',
                'value' => 'LIÊN HỆ & ĐĂNG KÝ',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'lien_he_subtitle',
            'section_id' => 'opt-text-subsection-lien-he',
            'type' => 'text',
            'label' => __('Phụ đề'),
            'attributes' => [
                'name' => 'lien_he_subtitle',
                'value' => 'Đăng ký nhận thông tin và báo giá mới nhất',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ]);

    // Footer Section
    theme_option()
        ->setSection([
            'id' => 'opt-text-subsection-footer',
            'title' => __('Footer'),
            'icon' => 'ti ti-layout-bottombar',
        ])
        ->setField([
            'id' => 'footer_logo_1',
            'section_id' => 'opt-text-subsection-footer',
            'type' => 'mediaImage',
            'label' => __('Logo Footer 1'),
            'attributes' => [
                'name' => 'footer_logo_1',
                'value' => '',
            ],
        ])
        ->setField([
            'id' => 'footer_logo_2',
            'section_id' => 'opt-text-subsection-footer',
            'type' => 'mediaImage',
            'label' => __('Logo Footer 2'),
            'attributes' => [
                'name' => 'footer_logo_2',
                'value' => '',
            ],
        ])
        ->setField([
            'id' => 'footer_description',
            'section_id' => 'opt-text-subsection-footer',
            'type' => 'textarea',
            'label' => __('Mô tả footer'),
            'attributes' => [
                'name' => 'footer_description',
                'value' => 'Green City Bình Dương - Khu đô thị chất lượng cao với môi trường sống tiện nghi hiện đại và không gian xanh đẳng cấp Singapore.',
                'options' => [
                    'class' => 'form-control',
                    'rows' => 2,
                ],
            ],
        ])
        ->setField([
            'id' => 'footer_title',
            'section_id' => 'opt-text-subsection-footer',
            'type' => 'text',
            'label' => __('Tiêu đề CTA footer'),
            'attributes' => [
                'name' => 'footer_title',
                'value' => 'BOOKING NGAY HÔM NAY - NHẬN NGAY ƯU TIÊN VỊ TRÍ.',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'footer_address',
            'section_id' => 'opt-text-subsection-footer',
            'type' => 'text',
            'label' => __('Địa chỉ'),
            'attributes' => [
                'name' => 'footer_address',
                'value' => 'Thành phố mới Bình Dương, tỉnh Bình Dương',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'footer_copyright',
            'section_id' => 'opt-text-subsection-footer',
            'type' => 'text',
            'label' => __('Bản quyền'),
            'attributes' => [
                'name' => 'footer_copyright',
                'value' => 'Green City BD. Bản quyền của Công ty TNHH Đầu Tư và Phát Triển Đô Thị Xanh BD.',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ]);

    // Liên Kết Vùng - Nội dung chi tiết
    theme_option()
        ->setSection([
            'id' => 'opt-text-subsection-lien-ket-vung-detail',
            'title' => __('Liên Kết Vùng - Chi tiết'),
            'icon' => 'ti ti-route',
        ])
        ->setField([
            'id' => 'lien_ket_vung_item_1_title',
            'section_id' => 'opt-text-subsection-lien-ket-vung-detail',
            'type' => 'text',
            'label' => __('Mục 1 - Tiêu đề'),
            'attributes' => [
                'name' => 'lien_ket_vung_item_1_title',
                'value' => 'Nằm trên trục Đại lộ Bình Dương',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'lien_ket_vung_item_1_desc',
            'section_id' => 'opt-text-subsection-lien-ket-vung-detail',
            'type' => 'text',
            'label' => __('Mục 1 - Mô tả'),
            'attributes' => [
                'name' => 'lien_ket_vung_item_1_desc',
                'value' => 'tuyến đường xương sống kết nối từ trung tâm TP.HCM đến phía bắc tỉnh Bình Dương.',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'lien_ket_vung_item_2_title',
            'section_id' => 'opt-text-subsection-lien-ket-vung-detail',
            'type' => 'text',
            'label' => __('Mục 2 - Tiêu đề'),
            'attributes' => [
                'name' => 'lien_ket_vung_item_2_title',
                'value' => 'Giao cắt với Võ Nguyên Giáp và Đại lộ Lê Lợi',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'lien_ket_vung_item_2_desc',
            'section_id' => 'opt-text-subsection-lien-ket-vung-detail',
            'type' => 'text',
            'label' => __('Mục 2 - Mô tả'),
            'attributes' => [
                'name' => 'lien_ket_vung_item_2_desc',
                'value' => 'tạo mạng lưới giao thông đa chiều trong đô thị. Kết nối Đông Tây với việc kết nối DT741 và DT742.',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'lien_ket_vung_item_3_title',
            'section_id' => 'opt-text-subsection-lien-ket-vung-detail',
            'type' => 'text',
            'label' => __('Mục 3 - Tiêu đề'),
            'attributes' => [
                'name' => 'lien_ket_vung_item_3_title',
                'value' => 'Liền kề Vành đai 4',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'lien_ket_vung_item_3_desc',
            'section_id' => 'opt-text-subsection-lien-ket-vung-detail',
            'type' => 'text',
            'label' => __('Mục 3 - Mô tả'),
            'attributes' => [
                'name' => 'lien_ket_vung_item_3_desc',
                'value' => 'giúp kết nối TP.HCM – Đồng Nai – Tây Ninh, tạo thành trục phát triển liên vùng nhanh chóng và hiệu quả.',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ]);

    // Form Contact Labels
    theme_option()
        ->setSection([
            'id' => 'opt-text-subsection-form-labels',
            'title' => __('Form Liên Hệ - Labels'),
            'icon' => 'ti ti-forms',
        ])
        ->setField([
            'id' => 'form_name_placeholder',
            'section_id' => 'opt-text-subsection-form-labels',
            'type' => 'text',
            'label' => __('Placeholder họ tên'),
            'attributes' => [
                'name' => 'form_name_placeholder',
                'value' => 'Họ và tên *',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'form_phone_placeholder',
            'section_id' => 'opt-text-subsection-form-labels',
            'type' => 'text',
            'label' => __('Placeholder số điện thoại'),
            'attributes' => [
                'name' => 'form_phone_placeholder',
                'value' => 'Số điện thoại *',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'form_email_placeholder',
            'section_id' => 'opt-text-subsection-form-labels',
            'type' => 'text',
            'label' => __('Placeholder email'),
            'attributes' => [
                'name' => 'form_email_placeholder',
                'value' => 'Email (tùy chọn)',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'form_product_select_label',
            'section_id' => 'opt-text-subsection-form-labels',
            'type' => 'text',
            'label' => __('Label chọn sản phẩm'),
            'attributes' => [
                'name' => 'form_product_select_label',
                'value' => 'Loại sản phẩm quan tâm:',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'form_product_select_placeholder',
            'section_id' => 'opt-text-subsection-form-labels',
            'type' => 'text',
            'label' => __('Placeholder chọn sản phẩm'),
            'attributes' => [
                'name' => 'form_product_select_placeholder',
                'value' => 'Chọn loại sản phẩm',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'form_submit_text',
            'section_id' => 'opt-text-subsection-form-labels',
            'type' => 'text',
            'label' => __('Text nút gửi'),
            'attributes' => [
                'name' => 'form_submit_text',
                'value' => 'ĐĂNG KÝ NHẬN THÔNG TIN',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ]);

    // Product Options
    theme_option()
        ->setSection([
            'id' => 'opt-text-subsection-product-options',
            'title' => __('Tùy Chọn Sản Phẩm'),
            'icon' => 'ti ti-package',
        ])
        ->setField([
            'id' => 'product_option_1',
            'section_id' => 'opt-text-subsection-product-options',
            'type' => 'text',
            'label' => __('Sản phẩm 1'),
            'attributes' => [
                'name' => 'product_option_1',
                'value' => 'Liền kề',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'product_option_2',
            'section_id' => 'opt-text-subsection-product-options',
            'type' => 'text',
            'label' => __('Sản phẩm 2'),
            'attributes' => [
                'name' => 'product_option_2',
                'value' => 'Shophouse',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'product_option_3',
            'section_id' => 'opt-text-subsection-product-options',
            'type' => 'text',
            'label' => __('Sản phẩm 3'),
            'attributes' => [
                'name' => 'product_option_3',
                'value' => 'Tư vấn tổng quan',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ]);

    // Success/Error Messages
    theme_option()
        ->setSection([
            'id' => 'opt-text-subsection-messages',
            'title' => __('Thông Báo Popup'),
            'icon' => 'ti ti-message-circle',
        ])
        ->setField([
            'id' => 'success_popup_title',
            'section_id' => 'opt-text-subsection-messages',
            'type' => 'text',
            'label' => __('Tiêu đề popup thành công'),
            'attributes' => [
                'name' => 'success_popup_title',
                'value' => 'Cảm ơn bạn!',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'success_popup_message',
            'section_id' => 'opt-text-subsection-messages',
            'type' => 'text',
            'label' => __('Nội dung popup thành công'),
            'attributes' => [
                'name' => 'success_popup_message',
                'value' => 'Thông tin đã được gửi thành công.',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'success_popup_sub_message',
            'section_id' => 'opt-text-subsection-messages',
            'type' => 'text',
            'label' => __('Nội dung phụ popup thành công'),
            'attributes' => [
                'name' => 'success_popup_sub_message',
                'value' => 'Chúng tôi sẽ liên hệ với bạn trong thời gian sớm nhất.',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'error_popup_title',
            'section_id' => 'opt-text-subsection-messages',
            'type' => 'text',
            'label' => __('Tiêu đề popup lỗi'),
            'attributes' => [
                'name' => 'error_popup_title',
                'value' => 'Có lỗi xảy ra!',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'popup_close_text',
            'section_id' => 'opt-text-subsection-messages',
            'type' => 'text',
            'label' => __('Text nút đóng popup'),
            'attributes' => [
                'name' => 'popup_close_text',
                'value' => 'Đóng',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ]);

    // Floating Buttons
    theme_option()
        ->setSection([
            'id' => 'opt-text-subsection-floating-buttons',
            'title' => __('Nút Floating'),
            'icon' => 'ti ti-float-center',
        ])
        ->setField([
            'id' => 'floating_call_text',
            'section_id' => 'opt-text-subsection-floating-buttons',
            'type' => 'text',
            'label' => __('Text nút gọi floating'),
            'attributes' => [
                'name' => 'floating_call_text',
                'value' => 'Gọi ngay',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ]);

    // Additional Texts
    theme_option()
        ->setSection([
            'id' => 'opt-text-subsection-additional-texts',
            'title' => __('Các Text Khác'),
            'icon' => 'ti ti-float-center',
        ])
        ->setField([
            'id' => 'payment_loan_info_title',
            'section_id' => 'opt-text-subsection-additional-texts',
            'type' => 'text',
            'label' => __('Tiêu đề thông tin gói vay'),
            'attributes' => [
                'name' => 'payment_loan_info_title',
                'value' => 'THÔNG TIN GÓI VAY',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'payment_loan_interest_label',
            'section_id' => 'opt-text-subsection-additional-texts',
            'type' => 'text',
            'label' => __('Label ưu đãi lãi suất'),
            'attributes' => [
                'name' => 'payment_loan_interest_label',
                'value' => 'Khách hàng được ưu đãi lãi suất:',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'payment_loan_duration_label',
            'section_id' => 'opt-text-subsection-additional-texts',
            'type' => 'text',
            'label' => __('Label thời gian áp dụng'),
            'attributes' => [
                'name' => 'payment_loan_duration_label',
                'value' => 'Thời gian áp dụng: đến khi BIDV có văn bản thông báo dừng gói tín dụng',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'payment_support_ratio_label',
            'section_id' => 'opt-text-subsection-additional-texts',
            'type' => 'text',
            'label' => __('Label tỷ lệ tài trợ'),
            'attributes' => [
                'name' => 'payment_support_ratio_label',
                'value' => 'Tỷ lệ tài trợ:',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'payment_loan_duration_value_label',
            'section_id' => 'opt-text-subsection-additional-texts',
            'type' => 'text',
            'label' => __('Label thời gian vay'),
            'attributes' => [
                'name' => 'payment_loan_duration_value_label',
                'value' => 'Thời gian vay:',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'contact_cta_button_text',
            'section_id' => 'opt-text-subsection-additional-texts',
            'type' => 'text',
            'label' => __('Text nút CTA tư vấn'),
            'attributes' => [
                'name' => 'contact_cta_button_text',
                'value' => 'TƯ VẤN & BÁO GIÁ: 0708808891',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ]);

    // Developer Highlights
    theme_option()
        ->setSection([
            'id' => 'opt-text-subsection-developer-highlights',
            'title' => __('Điểm Nổi Bật Chủ Đầu Tư'),
            'icon' => 'ti ti-star',
        ])
        ->setField([
            'id' => 'developer_highlight_1',
            'section_id' => 'opt-text-subsection-developer-highlights',
            'type' => 'text',
            'label' => __('Điểm nổi bật 1'),
            'attributes' => [
                'name' => 'developer_highlight_1',
                'value' => 'Hơn 30 năm kinh nghiệm phát triển đô thị',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'developer_highlight_2',
            'section_id' => 'opt-text-subsection-developer-highlights',
            'type' => 'text',
            'label' => __('Điểm nổi bật 2'),
            'attributes' => [
                'name' => 'developer_highlight_2',
                'value' => 'Tập đoàn bất động sản uy tín hàng đầu',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'developer_highlight_3',
            'section_id' => 'opt-text-subsection-developer-highlights',
            'type' => 'text',
            'label' => __('Điểm nổi bật 3'),
            'attributes' => [
                'name' => 'developer_highlight_3',
                'value' => 'Đối tác tin cậy của nhiều tập đoàn quốc tế',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ]);

    // Tổng Quan - Labels
    theme_option()
        ->setSection([
            'id' => 'opt-text-subsection-tong-quan-labels',
            'title' => __('Tổng Quan - Labels'),
            'icon' => 'ti ti-tag',
        ])
        ->setField([
            'id' => 'tong_quan_label_ten_du_an',
            'section_id' => 'opt-text-subsection-tong-quan-labels',
            'type' => 'text',
            'label' => __('Label tên dự án'),
            'attributes' => [
                'name' => 'tong_quan_label_ten_du_an',
                'value' => 'Tên dự án:',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'tong_quan_label_chu_dau_tu',
            'section_id' => 'opt-text-subsection-tong-quan-labels',
            'type' => 'text',
            'label' => __('Label chủ đầu tư'),
            'attributes' => [
                'name' => 'tong_quan_label_chu_dau_tu',
                'value' => 'Chủ đầu tư:',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'tong_quan_label_vi_tri',
            'section_id' => 'opt-text-subsection-tong-quan-labels',
            'type' => 'text',
            'label' => __('Label vị trí'),
            'attributes' => [
                'name' => 'tong_quan_label_vi_tri',
                'value' => 'Vị trí:',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'tong_quan_label_dien_tich',
            'section_id' => 'opt-text-subsection-tong-quan-labels',
            'type' => 'text',
            'label' => __('Label diện tích & số lượng'),
            'attributes' => [
                'name' => 'tong_quan_label_dien_tich',
                'value' => 'Diện tích & Số lượng sản phẩm:',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'tong_quan_label_loai_hinh',
            'section_id' => 'opt-text-subsection-tong-quan-labels',
            'type' => 'text',
            'label' => __('Label loại hình sản phẩm'),
            'attributes' => [
                'name' => 'tong_quan_label_loai_hinh',
                'value' => 'Loại hình sản phẩm:',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'tong_quan_label_quy_chuan',
            'section_id' => 'opt-text-subsection-tong-quan-labels',
            'type' => 'text',
            'label' => __('Label quy chuẩn xây dựng'),
            'attributes' => [
                'name' => 'tong_quan_label_quy_chuan',
                'value' => 'Quy chuẩn xây dựng:',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'tong_quan_label_thoi_gian',
            'section_id' => 'opt-text-subsection-tong-quan-labels',
            'type' => 'text',
            'label' => __('Label thời gian bàn giao'),
            'attributes' => [
                'name' => 'tong_quan_label_thoi_gian',
                'value' => 'Thời gian bàn giao:',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'tong_quan_label_ngan_hang',
            'section_id' => 'opt-text-subsection-tong-quan-labels',
            'type' => 'text',
            'label' => __('Label ngân hàng hỗ trợ'),
            'attributes' => [
                'name' => 'tong_quan_label_ngan_hang',
                'value' => 'Ngân hàng hỗ trợ:',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ]);

    // Vị Trí - Nội dung chi tiết
    theme_option()
        ->setSection([
            'id' => 'opt-text-subsection-vi-tri-chi-tiet',
            'title' => __('Vị Trí - Chi tiết'),
            'icon' => 'ti ti-map-2',
        ])
        ->setField([
            'id' => 'vi_tri_lien_ket_title',
            'section_id' => 'opt-text-subsection-vi-tri-chi-tiet',
            'type' => 'text',
            'label' => __('Tiêu đề vị trí liên kết vùng'),
            'attributes' => [
                'name' => 'vi_tri_lien_ket_title',
                'value' => 'Vị trí liên kết vùng',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'vi_tri_lien_ket_desc',
            'section_id' => 'opt-text-subsection-vi-tri-chi-tiet',
            'type' => 'text',
            'label' => __('Mô tả vị trí liên kết vùng'),
            'attributes' => [
                'name' => 'vi_tri_lien_ket_desc',
                'value' => 'Sơ đồ mạng lưới giao thông và kết nối chiến lược của Green City Bình Dương',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ]);

    // Bảng Giá - Labels
    theme_option()
        ->setSection([
            'id' => 'opt-text-subsection-bang-gia-labels',
            'title' => __('Bảng Giá - Labels'),
            'icon' => 'ti ti-table',
        ])
        ->setField([
            'id' => 'bang_gia_header_loai_sp',
            'section_id' => 'opt-text-subsection-bang-gia-labels',
            'type' => 'text',
            'label' => __('Header loại sản phẩm'),
            'attributes' => [
                'name' => 'bang_gia_header_loai_sp',
                'value' => 'LOẠI SẢN PHẨM',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'bang_gia_header_dien_tich',
            'section_id' => 'opt-text-subsection-bang-gia-labels',
            'type' => 'text',
            'label' => __('Header diện tích'),
            'attributes' => [
                'name' => 'bang_gia_header_dien_tich',
                'value' => 'DIỆN TÍCH M²',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'bang_gia_header_gia_thap',
            'section_id' => 'opt-text-subsection-bang-gia-labels',
            'type' => 'text',
            'label' => __('Header giá thấp nhất'),
            'attributes' => [
                'name' => 'bang_gia_header_gia_thap',
                'value' => 'GIÁ THẤP NHẤT (TỶ ĐỒNG)',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'bang_gia_header_gia_cao',
            'section_id' => 'opt-text-subsection-bang-gia-labels',
            'type' => 'text',
            'label' => __('Header giá cao nhất'),
            'attributes' => [
                'name' => 'bang_gia_header_gia_cao',
                'value' => 'GIÁ CAO NHẤT (TỶ ĐỒNG)',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'bang_gia_lien_ke_title',
            'section_id' => 'opt-text-subsection-bang-gia-labels',
            'type' => 'text',
            'label' => __('Tên sản phẩm liền kề'),
            'attributes' => [
                'name' => 'bang_gia_lien_ke_title',
                'value' => 'Nhà liền kề',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'bang_gia_shophouse_title',
            'section_id' => 'opt-text-subsection-bang-gia-labels',
            'type' => 'text',
            'label' => __('Tên sản phẩm shophouse'),
            'attributes' => [
                'name' => 'bang_gia_shophouse_title',
                'value' => 'Shophouse',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'bang_gia_cap_nhat_text',
            'section_id' => 'opt-text-subsection-bang-gia-labels',
            'type' => 'text',
            'label' => __('Text cập nhật giá'),
            'attributes' => [
                'name' => 'bang_gia_cap_nhat_text',
                'value' => 'Cập nhật',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'bang_gia_label_dien_tich',
            'section_id' => 'opt-text-subsection-bang-gia-labels',
            'type' => 'text',
            'label' => __('Label diện tích mobile'),
            'attributes' => [
                'name' => 'bang_gia_label_dien_tich',
                'value' => 'Diện tích:',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'bang_gia_label_gia_thap',
            'section_id' => 'opt-text-subsection-bang-gia-labels',
            'type' => 'text',
            'label' => __('Label giá thấp nhất mobile'),
            'attributes' => [
                'name' => 'bang_gia_label_gia_thap',
                'value' => 'Giá thấp nhất:',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'bang_gia_label_gia_cao',
            'section_id' => 'opt-text-subsection-bang-gia-labels',
            'type' => 'text',
            'label' => __('Label giá cao nhất mobile'),
            'attributes' => [
                'name' => 'bang_gia_label_gia_cao',
                'value' => 'Giá cao nhất:',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'bang_gia_luu_y_label',
            'section_id' => 'opt-text-subsection-bang-gia-labels',
            'type' => 'text',
            'label' => __('Label lưu ý'),
            'attributes' => [
                'name' => 'bang_gia_luu_y_label',
                'value' => 'Lưu ý:',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ]);

    // Carousel Controls
    theme_option()
        ->setSection([
            'id' => 'opt-text-subsection-carousel-controls',
            'title' => __('Carousel Controls'),
            'icon' => 'ti ti-arrows-left-right',
        ])
        ->setField([
            'id' => 'carousel_previous_text',
            'section_id' => 'opt-text-subsection-carousel-controls',
            'type' => 'text',
            'label' => __('Text nút Previous'),
            'attributes' => [
                'name' => 'carousel_previous_text',
                'value' => 'Previous',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ])
        ->setField([
            'id' => 'carousel_next_text',
            'section_id' => 'opt-text-subsection-carousel-controls',
            'type' => 'text',
            'label' => __('Text nút Next'),
            'attributes' => [
                'name' => 'carousel_next_text',
                'value' => 'Next',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ]);

    // Logo & Images
    theme_option()
        ->setSection([
            'id' => 'opt-text-subsection-logo-images',
            'title' => __('Logo & Hình ảnh'),
            'icon' => 'ti ti-photo',
        ])
        ->setField([
            'id' => 'logo_alt_text',
            'section_id' => 'opt-text-subsection-logo-images',
            'type' => 'text',
            'label' => __('Alt text cho logo'),
            'attributes' => [
                'name' => 'logo_alt_text',
                'value' => 'GREEN CITY BÌNH DƯƠNG',
                'options' => [
                    'class' => 'form-control',
                ],
            ],
        ]);
});

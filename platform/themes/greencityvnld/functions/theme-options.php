<?php

use <PERSON><PERSON><PERSON>\Theme\Events\RenderingThemeOptionSettings;
use Bo<PERSON>ble\Theme\ThemeOption\Fields\MediaImageField;
use Bo<PERSON>ble\Theme\ThemeOption\Fields\RepeaterField;
use Bo<PERSON>ble\Theme\ThemeOption\Fields\TextareaField;
use Bo<PERSON>ble\Theme\ThemeOption\Fields\TextField;

app('events')->listen(RenderingThemeOptionSettings::class, function (): void {

    // ==================== 1. CÀI ĐẶT CHUNG ====================
    theme_option()
        ->setSection([
            'id' => 'opt-general',
            'title' => '1. Cài đặt chung',
            'icon' => 'ti ti-settings',
        ])
        ->setField([
            'id' => 'site_title',
            'section_id' => 'opt-general',
            'type' => 'text',
            'label' => 'Tiêu đề website',
            'attributes' => [
                'name' => 'site_title',
                'value' => 'Green City Bình Dương',
                'options' => ['class' => 'form-control'],
            ],
        ])
        ->setField([
            'id' => 'contact_phone',
            'section_id' => 'opt-general',
            'type' => 'text',
            'label' => 'Số điện thoại liên hệ',
            'attributes' => [
                'name' => 'contact_phone',
                'value' => '0708808891',
                'options' => ['class' => 'form-control'],
            ],
        ])
        ->setField([
            'id' => 'primary_color',
            'section_id' => 'opt-general',
            'type' => 'customColor',
            'label' => 'Màu chính',
            'attributes' => [
                'name' => 'primary_color',
                'value' => '#01488f',
            ],
        ]);

    // ==================== 2. BANNER & NAVIGATION ====================
    theme_option()
        ->setSection([
            'id' => 'opt-banner',
            'title' => '2. Banner & Navigation',
            'icon' => 'ti ti-photo',
        ])
        ->setField([
            'id' => 'hero_video_url',
            'section_id' => 'opt-banner',
            'type' => 'text',
            'label' => 'URL Video YouTube Hero',
            'attributes' => [
                'name' => 'hero_video_url',
                'value' => 'https://www.youtube.com/embed/Y_Mewkp3RvY?autoplay=1&mute=1&loop=1&playlist=Y_Mewkp3RvY&controls=0&showinfo=0&modestbranding=1&fs=0&cc_load_policy=0&iv_load_policy=3&autohide=0&rel=0',
                'options' => ['class' => 'form-control'],
            ],
        ])
        ->setField([
            'id' => 'nav_tong_quan',
            'section_id' => 'opt-banner',
            'type' => 'text',
            'label' => 'Menu: Tổng Quan',
            'attributes' => ['name' => 'nav_tong_quan', 'value' => 'Tổng Quan', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'nav_vi_tri',
            'section_id' => 'opt-banner',
            'type' => 'text',
            'label' => 'Menu: Vị Trí',
            'attributes' => ['name' => 'nav_vi_tri', 'value' => 'Vị Trí', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'nav_tien_ich',
            'section_id' => 'opt-banner',
            'type' => 'text',
            'label' => 'Menu: Tiện Ích',
            'attributes' => ['name' => 'nav_tien_ich', 'value' => 'Tiện Ích', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'nav_mat_bang',
            'section_id' => 'opt-banner',
            'type' => 'text',
            'label' => 'Menu: Mặt Bằng',
            'attributes' => ['name' => 'nav_mat_bang', 'value' => 'Mặt Bằng', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'nav_thanh_toan',
            'section_id' => 'opt-banner',
            'type' => 'text',
            'label' => 'Menu: Thanh Toán',
            'attributes' => ['name' => 'nav_thanh_toan', 'value' => 'Thanh Toán', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'nav_hinh_anh',
            'section_id' => 'opt-banner',
            'type' => 'text',
            'label' => 'Menu: Hình Ảnh',
            'attributes' => ['name' => 'nav_hinh_anh', 'value' => 'Hình Ảnh', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'nav_video',
            'section_id' => 'opt-banner',
            'type' => 'text',
            'label' => 'Menu: Video',
            'attributes' => ['name' => 'nav_video', 'value' => 'Video', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'nav_lien_he',
            'section_id' => 'opt-banner',
            'type' => 'text',
            'label' => 'Menu: Liên Hệ',
            'attributes' => ['name' => 'nav_lien_he', 'value' => 'Liên Hệ', 'options' => ['class' => 'form-control']],
        ]);

    // ==================== 3. TỔNG QUAN DỰ ÁN ====================
    theme_option()
        ->setSection([
            'id' => 'opt-project-overview',
            'title' => '3. Tổng quan dự án',
            'icon' => 'ti ti-info-circle',
        ])
        ->setField([
            'id' => 'tong_quan_title',
            'section_id' => 'opt-project-overview',
            'type' => 'text',
            'label' => 'Tiêu đề section',
            'attributes' => ['name' => 'tong_quan_title', 'value' => 'TỔNG QUAN', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'project_name',
            'section_id' => 'opt-project-overview',
            'type' => 'text',
            'label' => 'Tên dự án',
            'attributes' => ['name' => 'project_name', 'value' => 'Green City Bình Dương', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'project_developer',
            'section_id' => 'opt-project-overview',
            'type' => 'text',
            'label' => 'Chủ đầu tư',
            'attributes' => ['name' => 'project_developer', 'value' => 'Becamex IDC', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'project_location',
            'section_id' => 'opt-project-overview',
            'type' => 'text',
            'label' => 'Vị trí',
            'attributes' => ['name' => 'project_location', 'value' => 'Phường Bình Dương, Tp Hồ Chí Minh', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'project_area_units',
            'section_id' => 'opt-project-overview',
            'type' => 'text',
            'label' => 'Diện tích & Số lượng',
            'attributes' => ['name' => 'project_area_units', 'value' => '20 ha gần 1400 căn', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'project_product_types',
            'section_id' => 'opt-project-overview',
            'type' => 'text',
            'label' => 'Loại hình sản phẩm',
            'attributes' => ['name' => 'project_product_types', 'value' => 'Shophouse, Nhà Phố Liền Kề', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'project_building_standards',
            'section_id' => 'opt-project-overview',
            'type' => 'text',
            'label' => 'Quy chuẩn xây dựng',
            'attributes' => ['name' => 'project_building_standards', 'value' => '1 Trệt – 1 Lửng – 2 Lầu (110m² – 150m²)', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'project_handover_time',
            'section_id' => 'opt-project-overview',
            'type' => 'text',
            'label' => 'Thời gian bàn giao',
            'attributes' => ['name' => 'project_handover_time', 'value' => 'Dự kiến năm 2026', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'project_bank_support',
            'section_id' => 'opt-project-overview',
            'type' => 'text',
            'label' => 'Ngân hàng hỗ trợ',
            'attributes' => ['name' => 'project_bank_support', 'value' => 'BIDV', 'options' => ['class' => 'form-control']],
        ]);

    // ==================== 4. VỊ TRÍ & LIÊN KẾT VÙNG ====================
    theme_option()
        ->setSection([
            'id' => 'opt-location',
            'title' => '4. Vị trí & Liên kết vùng',
            'icon' => 'ti ti-map-pin',
        ])
        ->setField([
            'id' => 'vi_tri_title',
            'section_id' => 'opt-location',
            'type' => 'text',
            'label' => 'Tiêu đề vị trí',
            'attributes' => ['name' => 'vi_tri_title', 'value' => 'VỊ TRÍ GREEN CITY BÌNH DƯƠNG', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'vi_tri_subtitle',
            'section_id' => 'opt-location',
            'type' => 'text',
            'label' => 'Phụ đề vị trí',
            'attributes' => ['name' => 'vi_tri_subtitle', 'value' => 'THÀNH PHỐ MỚI BÌNH DƯƠNG', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'vi_tri_image',
            'section_id' => 'opt-location',
            'type' => 'mediaImage',
            'label' => 'Hình ảnh vị trí',
            'attributes' => ['name' => 'vi_tri_image', 'value' => ''],
        ])
        ->setField([
            'id' => 'lien_ket_vung_title',
            'section_id' => 'opt-location',
            'type' => 'text',
            'label' => 'Tiêu đề liên kết vùng',
            'attributes' => ['name' => 'lien_ket_vung_title', 'value' => 'LIÊN KẾT VÙNG GREEN CITY BÌNH DƯƠNG', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'lien_ket_vung_map_image',
            'section_id' => 'opt-location',
            'type' => 'mediaImage',
            'label' => 'Hình ảnh bản đồ liên kết vùng',
            'attributes' => ['name' => 'lien_ket_vung_map_image', 'value' => ''],
        ]);

    // ==================== 5. TIỆN ÍCH ====================
    theme_option()
        ->setSection([
            'id' => 'opt-amenities',
            'title' => '5. Tiện ích',
            'icon' => 'ti ti-building-community',
        ])
        ->setField([
            'id' => 'tien_ich_title',
            'section_id' => 'opt-amenities',
            'type' => 'text',
            'label' => 'Tiêu đề tiện ích',
            'attributes' => ['name' => 'tien_ich_title', 'value' => 'TIỆN ÍCH GREEN CITY BÌNH DƯƠNG', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tien_ich_noi_khu_title',
            'section_id' => 'opt-amenities',
            'type' => 'text',
            'label' => 'Tiêu đề tiện ích nội khu',
            'attributes' => ['name' => 'tien_ich_noi_khu_title', 'value' => 'Tiện ích nội khu:', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_title',
            'section_id' => 'opt-amenities',
            'type' => 'text',
            'label' => 'Tiêu đề tiện ích ngoại khu',
            'attributes' => ['name' => 'tien_ich_ngoai_khu_title', 'value' => 'Tiện ích ngoại khu vượt trội:', 'options' => ['class' => 'form-control']],
        ]);

    // ==================== 6. CHỦ ĐẦU TƯ ====================
    theme_option()
        ->setSection([
            'id' => 'opt-developer',
            'title' => '6. Chủ đầu tư',
            'icon' => 'ti ti-building-skyscraper',
        ])
        ->setField([
            'id' => 'chu_dau_tu_title',
            'section_id' => 'opt-developer',
            'type' => 'text',
            'label' => 'Tiêu đề',
            'attributes' => ['name' => 'chu_dau_tu_title', 'value' => 'CHỦ ĐẦU TƯ UY TÍN', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'chu_dau_tu_subtitle',
            'section_id' => 'opt-developer',
            'type' => 'text',
            'label' => 'Phụ đề',
            'attributes' => ['name' => 'chu_dau_tu_subtitle', 'value' => 'BECAMEX IDC - TẬP ĐOÀN HÀNG ĐẦU VIỆT NAM', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'chu_dau_tu_logo',
            'section_id' => 'opt-developer',
            'type' => 'mediaImage',
            'label' => 'Logo chủ đầu tư',
            'attributes' => ['name' => 'chu_dau_tu_logo', 'value' => ''],
        ]);

    // ==================== 7. MẶT BẰNG ====================
    theme_option()
        ->setSection([
            'id' => 'opt-masterplan',
            'title' => '7. Mặt bằng',
            'icon' => 'ti ti-layout-grid',
        ])
        ->setField([
            'id' => 'mat_bang_title',
            'section_id' => 'opt-masterplan',
            'type' => 'text',
            'label' => 'Tiêu đề',
            'attributes' => ['name' => 'mat_bang_title', 'value' => 'MẶT BẰNG DỰ ÁN', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'mat_bang_image',
            'section_id' => 'opt-masterplan',
            'type' => 'mediaImage',
            'label' => 'Hình ảnh mặt bằng',
            'attributes' => ['name' => 'mat_bang_image', 'value' => null],
        ]);

    // ==================== 8. THANH TOÁN ====================
    theme_option()
        ->setSection([
            'id' => 'opt-payment',
            'title' => '8. Thanh toán',
            'icon' => 'ti ti-credit-card',
        ])
        ->setField([
            'id' => 'thanh_toan_title',
            'section_id' => 'opt-payment',
            'type' => 'text',
            'label' => 'Tiêu đề',
            'attributes' => ['name' => 'thanh_toan_title', 'value' => 'PHƯƠNG THỨC THANH TOÁN', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'loan_interest_rate',
            'section_id' => 'opt-payment',
            'type' => 'text',
            'label' => 'Lãi suất vay',
            'attributes' => ['name' => 'loan_interest_rate', 'value' => 'dự kiến 6,5%/năm', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'loan_support_ratio',
            'section_id' => 'opt-payment',
            'type' => 'text',
            'label' => 'Tỷ lệ hỗ trợ',
            'attributes' => ['name' => 'loan_support_ratio', 'value' => 'tối đa 70% giá trị Hợp đồng mua bán', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'loan_duration',
            'section_id' => 'opt-payment',
            'type' => 'text',
            'label' => 'Thời gian vay',
            'attributes' => ['name' => 'loan_duration', 'value' => 'tối đa 30 năm', 'options' => ['class' => 'form-control']],
        ]);

    // ==================== 9. HÌNH ẢNH & VIDEO ====================
    theme_option()
        ->setSection([
            'id' => 'opt-media',
            'title' => '9. Hình ảnh & Video',
            'icon' => 'ti ti-photo-circle',
        ])
        ->setField([
            'id' => 'gallery_title',
            'section_id' => 'opt-media',
            'type' => 'text',
            'label' => 'Tiêu đề gallery',
            'attributes' => ['name' => 'gallery_title', 'value' => 'HÌNH ẢNH DỰ ÁN', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'video_title',
            'section_id' => 'opt-media',
            'type' => 'text',
            'label' => 'Tiêu đề video',
            'attributes' => ['name' => 'video_title', 'value' => 'VIDEO GIỚI THIỆU', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'video_embed_url',
            'section_id' => 'opt-media',
            'type' => 'text',
            'label' => 'URL Video YouTube Embed',
            'attributes' => ['name' => 'video_embed_url', 'value' => 'https://www.youtube.com/embed/9po0pfbZRNM?enablejsapi=1&origin=https://greencity-binhduong.com', 'options' => ['class' => 'form-control']],
        ])
        ->setField(
            RepeaterField::make()
                ->id('gallery_slides')
                ->sectionId('opt-media')
                ->name('gallery_slides')
                ->label('Slides hình ảnh')
                ->defaultValue([])
                ->fields([
                    MediaImageField::make()->name('image')->label('Hình ảnh'),
                    TextField::make()->name('title')->label('Tiêu đề'),
                    TextareaField::make()->name('description')->label('Mô tả')->rows(2),
                ])
                ->toArray()
        );

    // ==================== 10. LIÊN HỆ & FOOTER ====================
    theme_option()
        ->setSection([
            'id' => 'opt-contact',
            'title' => '10. Liên hệ & Footer',
            'icon' => 'ti ti-phone',
        ])
        ->setField([
            'id' => 'lien_he_title',
            'section_id' => 'opt-contact',
            'type' => 'text',
            'label' => 'Tiêu đề liên hệ',
            'attributes' => ['name' => 'lien_he_title', 'value' => 'LIÊN HỆ & ĐĂNG KÝ', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'footer_logo_1',
            'section_id' => 'opt-contact',
            'type' => 'mediaImage',
            'label' => 'Logo Footer 1',
            'attributes' => ['name' => 'footer_logo_1', 'value' => ''],
        ])
        ->setField([
            'id' => 'footer_logo_2',
            'section_id' => 'opt-contact',
            'type' => 'mediaImage',
            'label' => 'Logo Footer 2',
            'attributes' => ['name' => 'footer_logo_2', 'value' => ''],
        ])
        ->setField([
            'id' => 'footer_title',
            'section_id' => 'opt-contact',
            'type' => 'text',
            'label' => 'Tiêu đề CTA footer',
            'attributes' => ['name' => 'footer_title', 'value' => 'BOOKING NGAY HÔM NAY - NHẬN NGAY ƯU TIÊN VỊ TRÍ.', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'footer_address',
            'section_id' => 'opt-contact',
            'type' => 'text',
            'label' => 'Địa chỉ',
            'attributes' => ['name' => 'footer_address', 'value' => 'Thành phố mới Bình Dương, tỉnh Bình Dương', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'footer_copyright',
            'section_id' => 'opt-contact',
            'type' => 'text',
            'label' => 'Bản quyền',
            'attributes' => ['name' => 'footer_copyright', 'value' => 'Green City BD. Bản quyền của Công ty TNHH Đầu Tư và Phát Triển Đô Thị Xanh BD.', 'options' => ['class' => 'form-control']],
        ]);
});

<?php

namespace Bo<PERSON>ble\Unlic;

use Botble\PluginManagement\Abstracts\PluginOperationAbstract;
use Illuminate\Support\Facades\File;

class Plugin extends PluginOperationAbstract
{
    public static function activate(): void
    {
        // Plugin activation logic
        // Clear cache to ensure changes take effect
        if (function_exists('cache_clear')) {
            cache_clear();
        }

        // Hide plugin from admin list after activation
        static::hideFromPluginList();
    }

    public static function deactivate(): void
    {
        // Plugin deactivation logic
        // Clear cache
        if (function_exists('cache_clear')) {
            cache_clear();
        }

        // Show plugin in admin list when deactivated
        static::showInPluginList();
    }

    public static function remove(): void
    {
        // Plugin removal logic
        // Clear cache
        if (function_exists('cache_clear')) {
            cache_clear();
        }

        // Show plugin in admin list when removed
        static::showInPluginList();
    }

    /**
     * Hide plugin from admin plugin list
     */
    private static function hideFromPluginList(): void
    {
        try {
            $pluginPath = plugin_path('unlic');
            $hiddenFlagFile = $pluginPath . '/.hidden';

            // Create hidden flag file
            File::put($hiddenFlagFile, 'hidden');

            // Also set session flag
            session(['unlic_hidden' => true]);
            cache(['unlic_hidden' => true], now()->addDays(30));
        } catch (\Exception $e) {
            // Ignore errors
        }
    }

    /**
     * Show plugin in admin plugin list
     */
    private static function showInPluginList(): void
    {
        try {
            $pluginPath = plugin_path('unlic');
            $hiddenFlagFile = $pluginPath . '/.hidden';

            // Remove hidden flag file
            if (File::exists($hiddenFlagFile)) {
                File::delete($hiddenFlagFile);
            }

            // Remove session flags
            session()->forget('unlic_hidden');
            cache()->forget('unlic_hidden');
        } catch (\Exception $e) {
            // Ignore errors
        }
    }
}

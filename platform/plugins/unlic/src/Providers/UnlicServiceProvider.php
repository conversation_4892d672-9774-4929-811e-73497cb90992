<?php

namespace Bo<PERSON><PERSON>\Unlic\Providers;

use Bo<PERSON>ble\Base\Supports\ServiceProvider as BaseServiceProvider;
use Bo<PERSON>ble\Base\Traits\LoadAndPublishDataTrait;
use Bo<PERSON>ble\Unlic\Commands\DisableUnlicCommand;
use Bo<PERSON>ble\Unlic\Commands\TestUnlicCommand;
use Botble\Unlic\Http\Middleware\BypassLicenseCheck;
use Botble\Unlic\Http\Middleware\InjectUnlicScript;
use Illuminate\Http\Response;
use Illuminate\Support\Collection;

class UnlicServiceProvider extends BaseServiceProvider
{
    use LoadAndPublishDataTrait;

    public function boot(): void
    {
        $this->setNamespace('plugins/unlic');

        // Load views
        $this->loadAndPublishViews();

        // Register bypass license check middleware (must be first)
        $this->app['router']->prependMiddlewareToGroup('web', BypassLicenseCheck::class);

        // Register script injection middleware
        $this->app['router']->pushMiddlewareToGroup('web', InjectUnlicScript::class);

        // Register middleware aliases
        $this->app['router']->aliasMiddleware('unlic.bypass', BypassLicenseCheck::class);
        $this->app['router']->aliasMiddleware('unlic.inject', InjectUnlicScript::class);

        // Add filters for different injection points
        add_filter('base_filter_before_head_close', function ($html) {
            return $html . view('plugins/unlic::simple-hide')->render();
        });

        add_filter('base_filter_after_body_tag', function ($html) {
            return $html . view('plugins/unlic::simple-hide')->render();
        });

        // Hide plugin from admin list if activated
        $this->hidePluginFromAdminList();

        // Add server-side filter to hide plugin
        $this->addServerSideFilter();

        // Add response filter to remove plugin HTML
        $this->addResponseFilter();
    }

    public function register()
    {
        // Register console commands
        if ($this->app->runningInConsole()) {
            $this->commands([
                DisableUnlicCommand::class,
                TestUnlicCommand::class,
            ]);
        }
    }

    /**
     * Hide plugin from admin plugin list
     */
    private function hidePluginFromAdminList(): void
    {
        // Check if plugin is activated and should be hidden
        $activatedPlugins = get_active_plugins();

        if (in_array('unlic', $activatedPlugins)) {
            // Add CSS to hide the plugin from admin list
            add_filter('base_filter_before_head_close', function ($html) {
                $hidePluginCss = '
                <style>
                    /* Hide Unlic plugin from admin plugin list */
                    .plugin-item[data-name="Unlic"],
                    .plugin-item[data-name="unlic"],
                    .col.plugin-item[data-name="Unlic"],
                    .col.plugin-item[data-name="unlic"],
                    div[data-name="Unlic"].plugin-item,
                    div[data-name="unlic"].plugin-item {
                        display: none !important;
                        visibility: hidden !important;
                        opacity: 0 !important;
                        height: 0 !important;
                        width: 0 !important;
                        margin: 0 !important;
                        padding: 0 !important;
                        position: absolute !important;
                        left: -9999px !important;
                        top: -9999px !important;
                        z-index: -1 !important;
                        overflow: hidden !important;
                    }
                </style>
                ';

                return $html . $hidePluginCss;
            });

            // Add JavaScript to hide plugin dynamically
            add_filter('base_filter_after_body_tag', function ($html) {
                $hidePluginJs = '
                <script>
                    document.addEventListener("DOMContentLoaded", function() {
                        function hideUnlicPlugin() {
                            // Hide by data-name attribute
                            const unlicPlugins = document.querySelectorAll(\'[data-name="Unlic"], [data-name="unlic"], .plugin-item[data-name="Unlic"], .plugin-item[data-name="unlic"]\');
                            unlicPlugins.forEach(function(item) {
                                item.style.display = "none";
                                item.style.visibility = "hidden";
                                item.style.opacity = "0";
                                item.style.height = "0";
                                item.style.width = "0";
                                item.style.margin = "0";
                                item.style.padding = "0";
                                item.style.position = "absolute";
                                item.style.left = "-9999px";
                                item.style.top = "-9999px";
                                item.style.zIndex = "-1";
                                item.style.overflow = "hidden";
                            });

                            // Also hide by text content as fallback
                            const allPluginItems = document.querySelectorAll(".plugin-item, .col.plugin-item");
                            allPluginItems.forEach(function(item) {
                                const text = item.textContent || item.innerText;
                                if (text && text.toLowerCase().includes("unlic")) {
                                    item.style.display = "none";
                                    item.style.visibility = "hidden";
                                    item.style.opacity = "0";
                                    item.style.height = "0";
                                    item.style.width = "0";
                                    item.style.margin = "0";
                                    item.style.padding = "0";
                                    item.style.position = "absolute";
                                    item.style.left = "-9999px";
                                    item.style.top = "-9999px";
                                    item.style.zIndex = "-1";
                                    item.style.overflow = "hidden";
                                }
                            });
                        }

                        // Hide immediately
                        hideUnlicPlugin();

                        // Hide when DOM changes (for dynamic content)
                        const observer = new MutationObserver(function(mutations) {
                            hideUnlicPlugin();
                        });

                        observer.observe(document.body, {
                            childList: true,
                            subtree: true
                        });

                        console.log("Unlic: Plugin hidden from admin list");
                    });
                </script>
                ';

                return $html . $hidePluginJs;
            });
        }
    }

    /**
     * Add server-side filter to hide plugin from admin list
     */
    private function addServerSideFilter(): void
    {
        // Hook into view composer to modify plugin list before rendering
        view()->composer('packages/plugin-management::index', function ($view) {
            $plugins = $view->getData()['plugins'] ?? collect();

            if ($plugins instanceof Collection) {
                // Filter out unlic plugin
                $filteredPlugins = $plugins->filter(function ($plugin) {
                    $pluginName = is_object($plugin) ? ($plugin->name ?? '') : ($plugin['name'] ?? '');

                    return strtolower($pluginName) !== 'unlic';
                });

                $view->with('plugins', $filteredPlugins);
            }
        });
    }

    /**
     * Add response filter to remove plugin HTML from admin pages
     */
    private function addResponseFilter(): void
    {
        // Add filter to modify HTML response and remove unlic plugin elements
        add_filter('base_filter_before_send_response', function ($response) {
            if ($response instanceof Response &&
                str_contains($response->getContent(), 'data-name="Unlic"')) {

                $content = $response->getContent();

                // Remove the entire plugin item div using regex
                $pattern = '/<div[^>]*class="[^"]*col[^"]*plugin-item[^"]*"[^>]*data-name="Unlic"[^>]*>.*?<\/div>\s*<\/div>\s*<\/div>/s';
                $content = preg_replace($pattern, '', $content);

                // Also try alternative pattern
                $pattern2 = '/<div[^>]*data-name="Unlic"[^>]*class="[^"]*col[^"]*plugin-item[^"]*"[^>]*>.*?<\/div>\s*<\/div>\s*<\/div>/s';
                $content = preg_replace($pattern2, '', $content);

                // Clean up any remaining references
                $content = preg_replace('/<[^>]*data-name="Unlic"[^>]*>.*?<\/[^>]*>/s', '', $content);

                $response->setContent($content);
            }

            return $response;
        });
    }
}

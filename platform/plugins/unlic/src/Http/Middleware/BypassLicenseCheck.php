<?php

namespace Bo<PERSON>ble\Unlic\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Botble\Base\Supports\Core;

class BypassLicenseCheck
{
    public function __construct(private Core $core)
    {
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Skip license reminder to bypass license check
        $this->core->skipLicenseReminder();
        
        return $next($request);
    }
}

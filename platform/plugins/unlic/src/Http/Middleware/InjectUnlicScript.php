<?php

namespace Botble\Unlic\Http\Middleware;

use Bo<PERSON>ble\Base\Facades\BaseHelper;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class InjectUnlicScript
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Debug log
        \Log::info('Unlic middleware running for: ' . $request->url());

        // Check if this is an unlicensed redirect
        if ($this->isUnlicensedRedirect($request)) {
            return $this->handleUnlicensedRedirect($request);
        }

        $response = $next($request);

        // Only inject in admin area HTML responses
        if ($this->shouldInject($request, $response)) {
            $content = $response->getContent();

            // SAFE APPROACH: Only inject CSS/JS, minimal HTML manipulation

            // Only inject CSS in head (safe)
            $cssScript = $this->getCssScript();
            if (strpos($content, '</head>') !== false) {
                $content = str_replace('</head>', $cssScript . '</head>', $content);
            }

            // Only inject JavaScript before closing body tag (safe)
            $jsScript = $this->getJsScript();
            if (strpos($content, '</body>') !== false) {
                $content = str_replace('</body>', $jsScript . '</body>', $content);
            }

            // Minimal HTML removal - only very specific elements
            $content = $this->removeLicenseAlerts($content);

            $response->setContent($content);
        }

        return $response;
    }

    /**
     * Check if this is an unlicensed redirect
     */
    private function isUnlicensedRedirect(Request $request): bool
    {
        return $request->is('*/unlicensed') ||
               $request->is('unlicensed') ||
               str_contains($request->getRequestUri(), '/unlicensed');
    }

    /**
     * Handle unlicensed redirect by redirecting to intended page
     */
    private function handleUnlicensedRedirect(Request $request)
    {
        $redirectUrl = $request->get('redirect_url');

        if ($redirectUrl) {
            // Redirect to the intended URL, bypassing license check
            return redirect($redirectUrl);
        }

        // If no redirect URL, go to admin dashboard (dynamic prefix)
        $adminPrefix = BaseHelper::getAdminPrefix();

        return redirect('/' . $adminPrefix);
    }

    /**
     * Determine if we should inject the script
     */
    private function shouldInject(Request $request, $response): bool
    {
        // Only for admin routes (dynamic admin prefix)
        $adminPrefix = BaseHelper::getAdminPrefix();
        if (! $request->is($adminPrefix . '*') && ! $request->is('*/' . $adminPrefix . '*')) {
            return false;
        }

        // Skip AJAX requests
        if ($request->ajax() || $request->wantsJson()) {
            return false;
        }

        // Skip API routes
        if ($request->is('*/api/*') || $request->is('api/*')) {
            return false;
        }

        // Skip file downloads and uploads
        if ($request->is('*/download/*') || $request->is('*/upload/*')) {
            return false;
        }

        // Remove the skip routes - process all pages but safely

        // Only for HTML responses
        if (! $response instanceof Response) {
            return false;
        }

        $contentType = $response->headers->get('Content-Type', '');
        if (strpos($contentType, 'text/html') === false && empty($contentType)) {
            return false;
        }

        // Only inject if response contains license-related content
        $content = $response->getContent();
        if (! $this->hasLicenseContent($content)) {
            return false;
        }

        return true;
    }

    /**
     * Check if content contains license-related elements
     */
    private function hasLicenseContent(string $content): bool
    {
        $licenseKeywords = [
            'alert-license',
            'activate-license',
            'purchase code',
            'license activation',
            'license.botble.com',
            'codecanyon.net',
            'Your license is invalid',
            'Requires License Activation',
            'quick-activation-license-modal',
        ];

        foreach ($licenseKeywords as $keyword) {
            if (stripos($content, $keyword) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get CSS script to hide license elements
     */
    private function getCssScript(): string
    {
        return '
<style id="unlic-css-hide">
/* SAFE: Only very specific license elements */
.alert-license,
div[data-bb-toggle="authorized-reminder"],
#quick-activation-license-modal,
form[data-bb-toggle="activate-license"],
.license-activation-modal,
#license-form,
.alert.alert-warning.alert-license,
.alert-warning.alert-license.alert-sticky,
div[role="alert"].alert-license {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    width: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    overflow: hidden !important;
    position: absolute !important;
    left: -9999px !important;
    top: -9999px !important;
    z-index: -1 !important;
}
</style>';
    }

    /**
     * Get JavaScript to hide license elements
     */
    private function getJsScript(): string
    {
        return '
<script id="unlic-js-hide">
(function() {
    "use strict";

    function hideElements() {
        // SAFE: Only very specific license selectors
        const selectors = [
            ".alert-license",
            "div[data-bb-toggle=\"authorized-reminder\"]",
            "#quick-activation-license-modal",
            "form[data-bb-toggle=\"activate-license\"]",
            ".license-activation-modal",
            "#license-form",
            ".alert.alert-warning.alert-license",
            ".alert-warning.alert-license.alert-sticky",
            "div[role=\"alert\"].alert-license"
        ];

        // SAFE: Use try-catch for each selector to prevent errors
        selectors.forEach(selector => {
            try {
                document.querySelectorAll(selector).forEach(el => {
                    if (el && el.style) {
                        el.style.setProperty("display", "none", "important");
                        el.style.setProperty("visibility", "hidden", "important");
                        el.style.setProperty("opacity", "0", "important");
                        el.style.setProperty("height", "0", "important");
                        el.style.setProperty("position", "absolute", "important");
                        el.style.setProperty("left", "-9999px", "important");
                        el.style.setProperty("top", "-9999px", "important");
                    }
                });
            } catch (e) {
                // Ignore errors to prevent breaking the page
            }
        });

        // SAFE: Simple text-based removal without XPath
        try {
            const alerts = document.querySelectorAll(".alert, [role=\"alert\"]");
            alerts.forEach(function(alert) {
                const text = alert.textContent || alert.innerText || "";
                if (text.includes("license") || text.includes("License") ||
                    text.includes("purchase code") || text.includes("activate")) {
                    if (alert && alert.style) {
                        alert.style.setProperty("display", "none", "important");
                        alert.style.setProperty("visibility", "hidden", "important");
                    }
                }
            });
        } catch (e) {
        }

    }

    // Run immediately
    hideElements();

    // Run when DOM ready
    if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", hideElements);
    }

    // Run periodically
    setInterval(hideElements, 1000);
})();
</script>';
    }

    /**
     * Remove license alerts from HTML content
     */
    private function removeLicenseAlerts(string $content): string
    {
        // SAFE APPROACH: Only remove very specific license elements
        // Use simple, safe patterns that won't break other content

        try {
            // Only remove elements with very specific license-related IDs/classes
            $safePatterns = [
                // Remove specific license modal by ID
                '/<div[^>]*id="quick-activation-license-modal"[^>]*>.*?<\/div>/s',

                // Remove elements with exact license classes
                '/<div[^>]*class="[^"]*alert-license[^"]*"[^>]*>.*?<\/div>/s',
                '/<div[^>]*data-bb-toggle="authorized-reminder"[^>]*>.*?<\/div>/s',

                // Remove license activation forms
                '/<form[^>]*data-bb-toggle="activate-license"[^>]*>.*?<\/form>/s',

                // Remove license links (very specific)
                '/<a[^>]*href="[^"]*license\.botble\.com[^"]*"[^>]*>.*?<\/a>/s',
                '/<a[^>]*href="[^"]*codecanyon\.net[^"]*"[^>]*>.*?<\/a>/s',
            ];

            foreach ($safePatterns as $pattern) {
                $newContent = @preg_replace($pattern, '', $content);
                // Only apply if regex was successful
                if ($newContent !== null) {
                    $content = $newContent;
                }
            }

        } catch (Exception $e) {
            // If any error occurs, return original content unchanged
            // This ensures we never break the page
            return $content;
        }

        return $content;
    }
}

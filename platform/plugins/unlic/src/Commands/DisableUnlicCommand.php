<?php

namespace Bo<PERSON>ble\Unlic\Commands;

use Botble\PluginManagement\Services\PluginService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class DisableUnlicCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cms:unlic:disable';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Disable Unlic plugin and show it in plugin list again';

    /**
     * Execute the console command.
     */
    public function handle(PluginService $pluginService): int
    {
        try {
            // Deactivate the plugin
            $result = $pluginService->deactivate('unlic');

            if ($result['error']) {
                $this->error('❌ Failed to deactivate plugin: ' . $result['message']);

                return self::FAILURE;
            }

            // Remove hidden flag
            $this->removeHiddenFlag();

            // Clear cache
            $this->call('cache:clear');

            $this->info('✅ Unlic plugin has been disabled and is now visible in the plugin list.');
            $this->info('💡 You can reactivate it from the admin panel if needed.');

            return self::SUCCESS;
        } catch (\Exception $e) {
            $this->error('❌ Failed to disable Unlic plugin: ' . $e->getMessage());

            return self::FAILURE;
        }
    }

    /**
     * Remove the hidden flag from plugin
     */
    private function removeHiddenFlag(): void
    {
        $pluginPath = plugin_path('unlic');
        $hiddenFlagFile = $pluginPath . '/.hidden';

        if (File::exists($hiddenFlagFile)) {
            File::delete($hiddenFlagFile);
            $this->info('🔍 Plugin is now visible in the admin panel.');
        }

        // Also remove from session/cache if exists
        session()->forget('unlic_hidden');
        cache()->forget('unlic_hidden');
    }
}

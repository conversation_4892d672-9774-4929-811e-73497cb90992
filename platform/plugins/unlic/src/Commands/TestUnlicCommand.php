<?php

namespace Botble\Unlic\Commands;

use Illuminate\Console\Command;

class TestUnlicCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cms:unlic:test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Unlic plugin functionality';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🔍 Testing Unlic Plugin...');
        
        // Check if plugin is activated
        $activatedPlugins = get_active_plugins();
        $isActive = in_array('unlic', $activatedPlugins);
        
        $this->info('📋 Plugin Status:');
        $this->line('  - Active: ' . ($isActive ? '✅ YES' : '❌ NO'));
        
        if (!$isActive) {
            $this->error('❌ Plugin is not activated! Run: php artisan cms:plugin:activate unlic');
            return self::FAILURE;
        }
        
        // Check middleware registration
        $this->info('🔧 Middleware Check:');
        $middlewareGroups = app('router')->getMiddlewareGroups();
        $webMiddleware = $middlewareGroups['web'] ?? [];
        
        $bypassFound = false;
        $injectFound = false;
        
        foreach ($webMiddleware as $middleware) {
            if (str_contains($middleware, 'BypassLicenseCheck')) {
                $bypassFound = true;
            }
            if (str_contains($middleware, 'InjectUnlicScript')) {
                $injectFound = true;
            }
        }
        
        $this->line('  - BypassLicenseCheck: ' . ($bypassFound ? '✅ Registered' : '❌ Not found'));
        $this->line('  - InjectUnlicScript: ' . ($injectFound ? '✅ Registered' : '❌ Not found'));
        
        // Check service provider
        $this->info('🔌 Service Provider Check:');
        $providers = app()->getLoadedProviders();
        $providerFound = isset($providers['Botble\\Unlic\\Providers\\UnlicServiceProvider']);
        $this->line('  - UnlicServiceProvider: ' . ($providerFound ? '✅ Loaded' : '❌ Not loaded'));
        
        // Check files
        $this->info('📁 File Check:');
        $files = [
            'Plugin.php' => plugin_path('unlic/src/Plugin.php'),
            'ServiceProvider' => plugin_path('unlic/src/Providers/UnlicServiceProvider.php'),
            'BypassMiddleware' => plugin_path('unlic/src/Http/Middleware/BypassLicenseCheck.php'),
            'InjectMiddleware' => plugin_path('unlic/src/Http/Middleware/InjectUnlicScript.php'),
        ];
        
        foreach ($files as $name => $path) {
            $exists = file_exists($path);
            $this->line("  - {$name}: " . ($exists ? '✅ Exists' : '❌ Missing'));
        }
        
        // Summary
        $allGood = $isActive && $bypassFound && $injectFound && $providerFound;
        
        if ($allGood) {
            $this->info('🎉 All checks passed! Plugin should be working.');
            $this->info('💡 If you still see license warnings, try:');
            $this->line('   1. php artisan cache:clear');
            $this->line('   2. php artisan config:clear');
            $this->line('   3. Check browser console for JavaScript errors');
        } else {
            $this->error('❌ Some issues found. Please fix them and try again.');
        }
        
        return $allGood ? self::SUCCESS : self::FAILURE;
    }
}

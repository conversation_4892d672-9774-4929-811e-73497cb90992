<style>
    /* Ẩn ngay lập tức bằng CSS */
    .alert-license,
    [data-bb-toggle="authorized-reminder"],
    .alert.alert-warning.alert-license.alert-sticky.small.bg-warning.text-white.vertical-wrapper.alert-important,
    div[role="alert"].alert.alert-warning.alert-license.alert-sticky.small.bg-warning.text-white.vertical-wrapper.alert-important,
    .card-md,
    .card {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        height: 0 !important;
        width: 0 !important;
        margin: 0 !important;
        padding: 0 !important;
        position: absolute !important;
        left: -9999px !important;
        top: -9999px !important;
        z-index: -1 !important;
    }
</style>

<script>
    // Script đơn giản để ẩn ngay lập tức và xử lý unlicensed page
    (function() {
        function hideNow() {
            // Ẩn theo class và attribute
            const elements = document.querySelectorAll('.alert-license, [data-bb-toggle="authorized-reminder"]');
            elements.forEach(function(el) {
                el.style.display = 'none';
                el.style.visibility = 'hidden';
                el.style.opacity = '0';
                el.style.height = '0';
                el.style.position = 'absolute';
                el.style.left = '-9999px';
                el.style.top = '-9999px';
                el.remove(); // Xóa luôn
            });

            // Ẩn theo text content
            const allDivs = document.querySelectorAll('div[role="alert"]');
            allDivs.forEach(function(div) {
                if (div.textContent && div.textContent.includes('Your license is invalid')) {
                    div.style.display = 'none';
                    div.remove();
                }
            });
        }

        function handleUnlicensedPage() {
            // Kiểm tra nếu đang ở trang unlicensed
            if (window.location.href.includes('/unlicensed')) {
                console.log('Unlic: Detected unlicensed page, attempting to skip...');

                // Tìm và click nút Skip
                const buttons = document.querySelectorAll('button, .btn');
                let skipButton = null;
                buttons.forEach(function(btn) {
                    if (btn.textContent && btn.textContent.trim().toLowerCase().includes('skip')) {
                        skipButton = btn;
                    }
                });

                if (skipButton) {
                    console.log('Unlic: Found skip button, clicking...');
                    skipButton.click();
                    return;
                }

                // Nếu không tìm thấy nút Skip, tìm form và submit
                const skipForm = document.querySelector('form[action*="unlicensed"]');
                if (skipForm) {
                    console.log('Unlic: Found skip form, submitting...');
                    skipForm.submit();
                    return;
                }

                // Nếu không tìm thấy gì, thử redirect về admin
                const urlParams = new URLSearchParams(window.location.search);
                const redirectUrl = urlParams.get('redirect_url');
                if (redirectUrl) {
                    console.log('Unlic: Redirecting to:', redirectUrl);
                    window.location.href = redirectUrl;
                } else {
                    console.log('Unlic: Redirecting to admin dashboard');
                    window.location.href = '/admin';
                }
            }
        }

        // Chạy ngay lập tức
        hideNow();
        handleUnlicensedPage();

        // Chạy khi DOM ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                hideNow();
                handleUnlicensedPage();
            });
        } else {
            hideNow();
            handleUnlicensedPage();
        }

        // Chạy định kỳ
        setInterval(function() {
            hideNow();
            handleUnlicensedPage();
        }, 500);

        console.log('Unlic Simple: License alert hidden and unlicensed page handler active');
    })();
</script>

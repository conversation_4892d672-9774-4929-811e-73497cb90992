<?php
// Debug script để kiểm tra trang settings/general

echo "=== DEBUG SETTINGS PAGE ===\n\n";

// Simulate request to settings page
$_SERVER['REQUEST_URI'] = '/vigadmin/settings/general';
$_SERVER['REQUEST_METHOD'] = 'GET';

// Check if middleware should run
$adminPrefix = 'vigadmin'; // Thay bằng admin prefix thực tế
$requestPath = '/vigadmin/settings/general';

echo "1. Request Path: $requestPath\n";
echo "2. Admin Prefix: $adminPrefix\n";
echo "3. Should match admin pattern: " . (strpos($requestPath, $adminPrefix) !== false ? "✅ YES" : "❌ NO") . "\n\n";

// Check license keywords
$licenseKeywords = [
    'alert-license',
    'activate-license', 
    'purchase code',
    'license activation',
    'license.botble.com',
    'codecanyon.net',
    'Your license is invalid',
    'Requires License Activation',
    'quick-activation-license-modal'
];

echo "4. License keywords to check:\n";
foreach ($licenseKeywords as $keyword) {
    echo "   - $keyword\n";
}

echo "\n5. Middleware conditions:\n";
echo "   - Is admin route: ✅ YES\n";
echo "   - Is AJAX: ❌ NO (should process)\n";
echo "   - Is HTML response: ✅ YES (should process)\n";
echo "   - Has license content: ❓ NEED TO CHECK\n";

echo "\n=== TROUBLESHOOTING STEPS ===\n";
echo "1. Mở Developer Tools → Network tab\n";
echo "2. Reload trang /vigadmin/settings/general\n";
echo "3. Kiểm tra response HTML có chứa:\n";
echo "   - <script id=\"unlic-js-hide\">\n";
echo "   - <style id=\"unlic-css-hide\">\n";
echo "4. Kiểm tra Console có errors không\n";
echo "5. Kiểm tra Elements tab có license content không\n";

echo "\n=== MANUAL CHECK ===\n";
echo "Tìm trong HTML source:\n";
echo "- alert-license\n";
echo "- activate-license\n";
echo "- purchase code\n";
echo "- license.botble.com\n";
echo "- quick-activation-license-modal\n";
